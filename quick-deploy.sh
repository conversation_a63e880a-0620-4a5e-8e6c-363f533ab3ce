#!/bin/bash

# 流量镜像工具一键部署脚本
# 专为运维人员设计的简化部署脚本

set -e

# 配置变量
APP_NAME="traffic-mirror"
APP_USER="traffic-mirror"
INSTALL_DIR="/opt/traffic-mirror"
SERVICE_NAME="traffic-mirror"
CONFIG_FILE="config.yaml"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${CYAN}[SUCCESS]${NC} $1"
}

# 显示横幅
show_banner() {
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                    流量镜像工具一键部署                        ║
║                    Traffic Mirror Quick Deploy                ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo
}

# 显示帮助信息
show_help() {
    cat << EOF
流量镜像工具一键部署脚本

用法: $0 [选项]

选项:
  -h, --help              显示此帮助信息
  -c, --config FILE       指定配置文件 (默认: config.yaml)
  -u, --user USER         指定运行用户 (默认: traffic-mirror)
  -d, --dir DIR           指定安装目录 (默认: /opt/traffic-mirror)
  --kafka-broker BROKER   设置Kafka地址
  --kafka-topic TOPIC     设置Kafka主题
  --target-api URL        设置目标API地址
  --dry-run              预览模式，不执行实际操作
  --uninstall            卸载应用
  --upgrade              升级模式

示例:
  $0                                    # 使用默认配置部署
  $0 --kafka-broker 10.1.1.100:9092   # 指定Kafka地址
  $0 --target-api http://api.example.com/v1/search  # 指定目标API
  $0 --dry-run                         # 预览部署过程
  $0 --uninstall                       # 卸载应用
  $0 --upgrade                         # 升级应用

配置文件示例:
  请编辑 config.yaml 文件，设置以下关键参数：
  - kafka.brokers: Kafka集群地址
  - kafka.topic: 消费的主题名称
  - target_api.url: 目标API地址
EOF
}

# 解析命令行参数
parse_args() {
    local dry_run=false
    local uninstall=false
    local upgrade=false
    local kafka_broker=""
    local kafka_topic=""
    local target_api=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            -u|--user)
                APP_USER="$2"
                shift 2
                ;;
            -d|--dir)
                INSTALL_DIR="$2"
                shift 2
                ;;
            --kafka-broker)
                kafka_broker="$2"
                shift 2
                ;;
            --kafka-topic)
                kafka_topic="$2"
                shift 2
                ;;
            --target-api)
                target_api="$2"
                shift 2
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            --uninstall)
                uninstall=true
                shift
                ;;
            --upgrade)
                upgrade=true
                shift
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 导出变量
    export DRY_RUN=$dry_run
    export UNINSTALL=$uninstall
    export UPGRADE=$upgrade
    export KAFKA_BROKER=$kafka_broker
    export KAFKA_TOPIC=$kafka_topic
    export TARGET_API=$target_api
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统要求
check_system_requirements() {
    log_step "检查系统要求..."
    
    # 检查操作系统
    if ! command -v systemctl &> /dev/null; then
        log_error "需要systemd支持的Linux系统"
        exit 1
    fi
    
    # 检查磁盘空间（至少1GB）
    available_space=$(df / | awk 'NR==2 {print $4}')
    if [ $available_space -lt 1048576 ]; then
        log_warn "可用磁盘空间不足1GB，建议清理磁盘空间"
    fi
    
    # 检查必要的命令
    local required_commands=("curl" "tar" "systemctl")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            log_error "缺少必要命令: $cmd"
            exit 1
        fi
    done
    
    log_info "系统要求检查通过"
}

# 检查应用文件
check_application_files() {
    log_step "检查应用文件..."
    
    # 检查二进制文件
    if [ ! -f "$APP_NAME" ]; then
        log_error "未找到应用程序文件: $APP_NAME"
        log_info "请确保在解压后的应用目录中运行此脚本"
        exit 1
    fi
    
    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "未找到配置文件: $CONFIG_FILE"
        exit 1
    fi
    
    # 检查服务文件
    if [ ! -f "traffic-mirror.service" ]; then
        log_error "未找到systemd服务文件: traffic-mirror.service"
        exit 1
    fi
    
    log_info "应用文件检查通过"
}

# 显示配置预览
show_configuration_preview() {
    log_step "配置预览"
    
    echo "部署配置："
    echo "  应用名称: $APP_NAME"
    echo "  运行用户: $APP_USER"
    echo "  安装目录: $INSTALL_DIR"
    echo "  配置文件: $CONFIG_FILE"
    echo "  服务名称: $SERVICE_NAME"
    echo
    
    # 显示关键配置项
    if [ -f "$CONFIG_FILE" ]; then
        echo "关键配置项："
        if command -v grep &> /dev/null; then
            echo "  Kafka地址: $(grep -A1 'brokers:' "$CONFIG_FILE" | grep -o '"[^"]*"' | head -1 || echo '未配置')"
            echo "  Kafka主题: $(grep 'topic:' "$CONFIG_FILE" | awk '{print $2}' | tr -d '"' || echo '未配置')"
            echo "  目标API: $(grep 'url:' "$CONFIG_FILE" | awk '{print $2}' | tr -d '"' || echo '未配置')"
        fi
    fi
    echo
}

# 确认部署
confirm_deployment() {
    if [ "$DRY_RUN" = true ]; then
        log_warn "预览模式 - 不会执行实际操作"
        return
    fi
    
    echo -e "${YELLOW}是否继续部署？${NC}"
    echo "输入 'yes' 确认，或按 Ctrl+C 取消"
    read -p "> " -r
    
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
}

# 创建用户和目录
setup_user_and_directories() {
    log_step "创建用户和目录..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] 将创建用户 $APP_USER 和目录 $INSTALL_DIR"
        return
    fi
    
    # 创建用户
    if id "$APP_USER" &>/dev/null; then
        log_info "用户 $APP_USER 已存在"
    else
        useradd -r -s /bin/false -d "$INSTALL_DIR" "$APP_USER"
        log_info "用户 $APP_USER 创建成功"
    fi
    
    # 创建目录
    mkdir -p "$INSTALL_DIR"/{logs,backup}
    chown -R "$APP_USER:$APP_USER" "$INSTALL_DIR"
    chmod 755 "$INSTALL_DIR"
    
    log_info "目录结构创建完成"
}

# 安装应用文件
install_application() {
    log_step "安装应用文件..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] 将安装应用文件到 $INSTALL_DIR"
        return
    fi
    
    # 备份现有文件
    if [ -f "$INSTALL_DIR/$APP_NAME" ]; then
        log_info "备份现有应用文件"
        cp "$INSTALL_DIR/$APP_NAME" "$INSTALL_DIR/backup/$APP_NAME.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # 复制文件
    cp "$APP_NAME" "$INSTALL_DIR/"
    cp "$CONFIG_FILE" "$INSTALL_DIR/"
    
    # 复制其他文件
    [ -f "check_status.sh" ] && cp "check_status.sh" "$INSTALL_DIR/"
    [ -f "README.md" ] && cp "README.md" "$INSTALL_DIR/"
    [ -f "USAGE.md" ] && cp "USAGE.md" "$INSTALL_DIR/"
    [ -f "VERSION" ] && cp "VERSION" "$INSTALL_DIR/"
    
    # 设置权限
    chown -R "$APP_USER:$APP_USER" "$INSTALL_DIR"
    chmod 755 "$INSTALL_DIR/$APP_NAME"
    chmod 644 "$INSTALL_DIR/$CONFIG_FILE"
    
    log_info "应用文件安装完成"
}

# 更新配置文件
update_configuration() {
    if [ -z "$KAFKA_BROKER" ] && [ -z "$KAFKA_TOPIC" ] && [ -z "$TARGET_API" ]; then
        return
    fi
    
    log_step "更新配置文件..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] 将更新配置文件"
        [ -n "$KAFKA_BROKER" ] && log_info "[DRY RUN] Kafka地址: $KAFKA_BROKER"
        [ -n "$KAFKA_TOPIC" ] && log_info "[DRY RUN] Kafka主题: $KAFKA_TOPIC"
        [ -n "$TARGET_API" ] && log_info "[DRY RUN] 目标API: $TARGET_API"
        return
    fi
    
    local config_path="$INSTALL_DIR/$CONFIG_FILE"
    
    # 更新Kafka地址
    if [ -n "$KAFKA_BROKER" ]; then
        sed -i "s|brokers:.*|brokers:\n    - \"$KAFKA_BROKER\"|" "$config_path"
        log_info "已更新Kafka地址: $KAFKA_BROKER"
    fi
    
    # 更新Kafka主题
    if [ -n "$KAFKA_TOPIC" ]; then
        sed -i "s|topic:.*|topic: \"$KAFKA_TOPIC\"|" "$config_path"
        log_info "已更新Kafka主题: $KAFKA_TOPIC"
    fi
    
    # 更新目标API
    if [ -n "$TARGET_API" ]; then
        sed -i "s|url:.*|url: \"$TARGET_API\"|" "$config_path"
        log_info "已更新目标API: $TARGET_API"
    fi
}

# 安装systemd服务
install_systemd_service() {
    log_step "安装systemd服务..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] 将安装systemd服务"
        return
    fi
    
    # 更新服务文件中的路径
    sed "s|/opt/traffic-mirror|$INSTALL_DIR|g" traffic-mirror.service > /etc/systemd/system/$SERVICE_NAME.service
    sed -i "s|User=traffic-mirror|User=$APP_USER|g" /etc/systemd/system/$SERVICE_NAME.service
    
    # 重新加载systemd
    systemctl daemon-reload
    systemctl enable "$SERVICE_NAME"
    
    log_info "systemd服务安装完成"
}

# 启动服务
start_service() {
    log_step "启动服务..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] 将启动服务 $SERVICE_NAME"
        return
    fi
    
    systemctl start "$SERVICE_NAME"
    
    # 等待服务启动
    sleep 3
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "服务启动成功"
    else
        log_error "服务启动失败"
        systemctl status "$SERVICE_NAME" --no-pager
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_step "验证部署..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] 将验证部署"
        return
    fi
    
    # 检查进程
    if pgrep -f "$APP_NAME" > /dev/null; then
        log_info "✓ 进程运行正常"
    else
        log_error "✗ 进程未运行"
        return 1
    fi
    
    # 检查服务状态
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_info "✓ 服务状态正常"
    else
        log_error "✗ 服务状态异常"
        return 1
    fi
    
    # 运行健康检查（如果存在）
    if [ -f "$INSTALL_DIR/check_status.sh" ]; then
        log_info "运行健康检查..."
        cd "$INSTALL_DIR"
        chmod +x check_status.sh
        ./check_status.sh || log_warn "健康检查发现问题，请查看日志"
        cd - > /dev/null
    fi
    
    log_success "部署验证完成"
}

# 卸载应用
uninstall_application() {
    log_step "卸载应用..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] 将卸载应用"
        return
    fi
    
    # 停止服务
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        systemctl stop "$SERVICE_NAME"
        log_info "服务已停止"
    fi
    
    # 禁用服务
    if systemctl is-enabled --quiet "$SERVICE_NAME"; then
        systemctl disable "$SERVICE_NAME"
        log_info "服务已禁用"
    fi
    
    # 删除服务文件
    if [ -f "/etc/systemd/system/$SERVICE_NAME.service" ]; then
        rm -f "/etc/systemd/system/$SERVICE_NAME.service"
        systemctl daemon-reload
        log_info "服务文件已删除"
    fi
    
    # 备份并删除应用目录
    if [ -d "$INSTALL_DIR" ]; then
        backup_dir="/tmp/${APP_NAME}_backup_$(date +%Y%m%d_%H%M%S)"
        mv "$INSTALL_DIR" "$backup_dir"
        log_info "应用目录已备份到: $backup_dir"
    fi
    
    # 删除用户（可选）
    echo -e "${YELLOW}是否删除用户 $APP_USER？(y/N)${NC}"
    read -p "> " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        userdel "$APP_USER" 2>/dev/null || true
        log_info "用户 $APP_USER 已删除"
    fi
    
    log_success "应用卸载完成"
}

# 显示部署结果
show_deployment_results() {
    log_step "部署完成"
    
    cat << EOF

🎉 流量镜像工具部署成功！

📋 部署信息:
  应用名称: $APP_NAME
  安装目录: $INSTALL_DIR
  运行用户: $APP_USER
  服务名称: $SERVICE_NAME

🚀 管理命令:
  启动服务: sudo systemctl start $SERVICE_NAME
  停止服务: sudo systemctl stop $SERVICE_NAME
  重启服务: sudo systemctl restart $SERVICE_NAME
  查看状态: sudo systemctl status $SERVICE_NAME
  查看日志: sudo journalctl -u $SERVICE_NAME -f

📊 监控命令:
  健康检查: cd $INSTALL_DIR && ./check_status.sh
  请求日志: tail -f $INSTALL_DIR/logs/request.log
  错误日志: tail -f $INSTALL_DIR/logs/error.log

📝 配置文件: $INSTALL_DIR/$CONFIG_FILE
📖 使用指南: $INSTALL_DIR/USAGE.md

⚠️  重要提醒:
1. 请根据您的环境修改配置文件中的Kafka地址和目标API地址
2. 修改配置后需要重启服务: sudo systemctl restart $SERVICE_NAME
3. 定期检查日志文件，确保服务正常运行

EOF
}

# 主函数
main() {
    show_banner
    parse_args "$@"
    
    # 处理卸载
    if [ "$UNINSTALL" = true ]; then
        check_permissions
        uninstall_application
        exit 0
    fi
    
    # 正常部署流程
    check_permissions
    check_system_requirements
    check_application_files
    show_configuration_preview
    confirm_deployment
    
    setup_user_and_directories
    install_application
    update_configuration
    install_systemd_service
    start_service
    verify_deployment
    show_deployment_results
    
    log_success "部署流程完成！"
}

# 执行主函数
main "$@"
