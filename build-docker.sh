#!/bin/bash

# 流量镜像工具 Docker 构建脚本
# 基于项目现有架构

set -e

# 配置变量
IMAGE_NAME="traffic-mirror"
IMAGE_TAG="latest"
DOCKERFILE="Dockerfile.traffic-mirror"
REGISTRY="artifacts.iflytek.com/docker-private/datahub/lynxiao"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
流量镜像工具 Docker 构建脚本

用法: $0 [选项]

选项:
  -h, --help              显示此帮助信息
  -t, --tag TAG           指定镜像标签 (默认: latest)
  -r, --registry REGISTRY 指定镜像仓库 (默认: artifacts.iflytek.com/docker-private/datahub/lynxiao)
  -f, --dockerfile FILE   指定Dockerfile (默认: Dockerfile.traffic-mirror)
  --no-cache             不使用缓存构建
  --push                 构建后推送到仓库
  --build-only           只构建，不推送
  --version VERSION      指定版本号

环境变量:
  BUILDER                构建用户名
  DEPEND_APIKEY          依赖仓库API密钥

示例:
  $0                                    # 使用默认配置构建
  $0 --tag v1.0.0 --push              # 构建并推送v1.0.0版本
  $0 --no-cache --version 1.0.1       # 无缓存构建指定版本
  $0 --registry my-registry.com        # 使用自定义仓库

EOF
}

# 解析命令行参数
parse_args() {
    local no_cache=false
    local push=false
    local build_only=false
    local version=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -t|--tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            -r|--registry)
                REGISTRY="$2"
                shift 2
                ;;
            -f|--dockerfile)
                DOCKERFILE="$2"
                shift 2
                ;;
            --no-cache)
                no_cache=true
                shift
                ;;
            --push)
                push=true
                shift
                ;;
            --build-only)
                build_only=true
                shift
                ;;
            --version)
                version="$2"
                shift 2
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 导出变量
    export NO_CACHE=$no_cache
    export PUSH=$push
    export BUILD_ONLY=$build_only
    export VERSION=${version:-"1.0.0"}
}

# 检查环境
check_environment() {
    log_step "检查构建环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi
    
    # 检查Dockerfile
    if [ ! -f "$DOCKERFILE" ]; then
        log_error "Dockerfile不存在: $DOCKERFILE"
        exit 1
    fi
    
    # 检查必要的环境变量
    if [ -z "$BUILDER" ]; then
        log_warn "BUILDER环境变量未设置，可能影响依赖下载"
    fi
    
    if [ -z "$DEPEND_APIKEY" ]; then
        log_warn "DEPEND_APIKEY环境变量未设置，可能影响依赖下载"
    fi
    
    log_info "环境检查通过"
}

# 显示构建信息
show_build_info() {
    log_step "构建信息"
    
    echo "镜像配置："
    echo "  名称: $IMAGE_NAME"
    echo "  标签: $IMAGE_TAG"
    echo "  仓库: $REGISTRY"
    echo "  完整名称: $REGISTRY/$IMAGE_NAME:$IMAGE_TAG"
    echo "  Dockerfile: $DOCKERFILE"
    echo "  版本: $VERSION"
    echo
    
    echo "构建选项："
    echo "  无缓存: $NO_CACHE"
    echo "  推送: $PUSH"
    echo "  只构建: $BUILD_ONLY"
    echo
    
    echo "环境变量："
    echo "  BUILDER: ${BUILDER:-未设置}"
    echo "  DEPEND_APIKEY: ${DEPEND_APIKEY:+已设置}"
    echo
}

# 构建镜像
build_image() {
    log_step "构建Docker镜像..."
    
    local full_image_name="$REGISTRY/$IMAGE_NAME:$IMAGE_TAG"
    local build_args=""
    
    # 构建参数
    build_args="--build-arg VERSION=$VERSION"
    build_args="$build_args --build-arg BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)"
    build_args="$build_args --build-arg GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"
    
    if [ -n "$BUILDER" ]; then
        build_args="$build_args --build-arg BUILDER=$BUILDER"
    fi
    
    if [ -n "$DEPEND_APIKEY" ]; then
        build_args="$build_args --build-arg DEPEND_APIKEY=$DEPEND_APIKEY"
    fi
    
    # 无缓存选项
    if [ "$NO_CACHE" = true ]; then
        build_args="$build_args --no-cache"
    fi
    
    # 执行构建
    log_info "执行构建命令..."
    docker build \
        -f "$DOCKERFILE" \
        -t "$full_image_name" \
        $build_args \
        .
    
    if [ $? -eq 0 ]; then
        log_info "镜像构建成功: $full_image_name"
    else
        log_error "镜像构建失败"
        exit 1
    fi
}

# 推送镜像
push_image() {
    if [ "$BUILD_ONLY" = true ]; then
        log_info "只构建模式，跳过推送"
        return
    fi
    
    if [ "$PUSH" = false ]; then
        log_info "未指定推送，跳过推送步骤"
        return
    fi
    
    log_step "推送Docker镜像..."
    
    local full_image_name="$REGISTRY/$IMAGE_NAME:$IMAGE_TAG"
    
    log_info "推送镜像: $full_image_name"
    docker push "$full_image_name"
    
    if [ $? -eq 0 ]; then
        log_info "镜像推送成功"
    else
        log_error "镜像推送失败"
        exit 1
    fi
}

# 显示结果
show_results() {
    log_step "构建完成"
    
    local full_image_name="$REGISTRY/$IMAGE_NAME:$IMAGE_TAG"
    
    cat << EOF

🎉 Docker镜像构建完成！

📋 镜像信息:
  镜像名称: $full_image_name
  版本: $VERSION
  大小: $(docker images --format "table {{.Size}}" "$full_image_name" | tail -n 1)

🚀 使用方法:
  # 运行容器
  docker run -d \\
    --name traffic-mirror \\
    -e KAFKA_BROKERS=************:19092 \\
    -e KAFKA_TOPIC=lynxiao_flow \\
    -e TARGET_API_URL=http://localhost:8080/v1/search \\
    -v ./logs:/app/logs \\
    $full_image_name

  # 使用Docker Compose
  docker-compose -f docker-compose.traffic-mirror.yml up -d

📊 管理命令:
  查看镜像: docker images | grep $IMAGE_NAME
  删除镜像: docker rmi $full_image_name
  查看容器: docker ps | grep traffic-mirror

EOF
}

# 主函数
main() {
    parse_args "$@"
    check_environment
    show_build_info
    
    # 确认构建
    if [ -t 0 ]; then  # 检查是否在交互式终端中
        echo -e "${YELLOW}是否继续构建？(y/N)${NC}"
        read -p "> " -r
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "构建已取消"
            exit 0
        fi
    fi
    
    build_image
    push_image
    show_results
    
    log_info "构建流程完成！"
}

# 执行主函数
main "$@"
