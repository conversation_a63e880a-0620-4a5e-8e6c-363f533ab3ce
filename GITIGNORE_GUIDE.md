# .gitignore 配置指南

## 📋 概述

本项目的 `.gitignore` 文件经过精心设计，确保只有源代码和必要的配置文件被纳入版本控制，而忽略所有生成的、临时的和敏感的文件。

## 🎯 忽略文件分类

### 1. 二进制文件和可执行文件

```bash
# 主程序二进制文件
traffic-mirror
traffic-mirror.exe

# 平台特定的二进制文件
traffic-mirror-*
*-amd64
*-arm64
*-linux-*
*-darwin-*
*-windows-*
```

**原因**: 二进制文件是编译产物，不应该存储在版本控制中，应该通过CI/CD构建生成。

### 2. 日志文件

```bash
# 应用日志
*.log
*.log.*
logs/
log/

# 日志轮转文件
*.log.gz
*.log.bz2
request.log*
error.log*
```

**原因**: 日志文件是运行时产生的，包含大量数据且经常变化，不适合版本控制。

### 3. 压缩包和归档文件

```bash
# 发行包
*.tar.gz
*.zip
traffic-mirror-*.tar.gz

# 备份文件
*.backup
*.bak
*.old
```

**原因**: 压缩包通常是发布产物或备份文件，不需要存储在源码仓库中。

### 4. 构建和发布目录

```bash
# 构建目录
build/
dist/
packages/
releases/

# 临时构建文件
temp/
tmp/
```

**原因**: 这些目录包含构建过程中生成的文件，应该通过构建脚本重新生成。

### 5. Go语言特定文件

```bash
# 依赖管理
vendor/

# 测试覆盖率文件
coverage.out
coverage.html

# 性能分析文件
*.prof
*.pprof
```

**原因**: Go的vendor目录、测试覆盖率报告和性能分析文件都是可重新生成的。

### 6. 开发工具和IDE文件

```bash
# Visual Studio Code
.vscode/

# GoLand/IntelliJ IDEA
.idea/

# Vim
*.swp
*.swo
```

**原因**: IDE配置文件是个人偏好，不应该强制给其他开发者。

### 7. 操作系统文件

```bash
# macOS
.DS_Store

# Windows
Thumbs.db
Desktop.ini

# Linux
.directory
.Trash-*
```

**原因**: 操作系统自动生成的文件对项目无用，且会污染仓库。

### 8. 配置和敏感文件

```bash
# 环境配置文件
.env
.env.local

# 敏感配置文件
config_secret.yaml
*_private.yaml

# 密钥和证书文件
*.key
*.pem
*.crt
```

**原因**: 敏感信息不应该存储在版本控制中，避免安全风险。

## ✅ 应该提交的文件

以下文件**应该**被纳入版本控制：

### 源代码文件
```bash
*.go           # Go源代码
*.md           # 文档文件
*.yaml         # 配置模板文件
*.yml          # 配置模板文件
```

### 配置模板
```bash
config_prod.yaml    # 生产环境配置模板
config_dev.yaml     # 开发环境配置模板
```

### 构建和部署脚本
```bash
Makefile           # 构建管理
build.sh           # 构建脚本
deploy.sh          # 部署脚本
quick-deploy.sh    # 一键部署脚本
```

### 容器化文件
```bash
Dockerfile         # Docker镜像构建
docker-compose.yml # 容器编排
```

### 项目管理文件
```bash
go.mod             # Go模块定义
go.sum             # Go模块校验和
VERSION            # 版本信息
LICENSE            # 许可证
```

## 🔧 .gitignore 最佳实践

### 1. 分类组织

使用注释将不同类型的忽略规则分组，便于维护：

```bash
# =============================================================================
# 二进制文件和可执行文件
# =============================================================================
```

### 2. 具体优于通用

优先使用具体的文件名或路径，而不是过于宽泛的模式：

```bash
# 好的做法
traffic-mirror
traffic-mirror.exe

# 避免过于宽泛
*
```

### 3. 注释说明

为复杂的忽略规则添加注释说明：

```bash
# 敏感配置文件（保留模板）
config_secret.yaml
*_private.yaml
```

### 4. 测试忽略规则

使用 `git check-ignore` 命令测试文件是否被正确忽略：

```bash
# 测试单个文件
git check-ignore test.log

# 测试多个文件
git check-ignore *.log

# 显示匹配的规则
git check-ignore -v test.log
```

## 🚨 常见问题

### 1. 文件已经被跟踪

如果文件已经被git跟踪，添加到.gitignore后不会自动忽略：

```bash
# 停止跟踪但保留文件
git rm --cached filename

# 停止跟踪整个目录
git rm -r --cached directory/
```

### 2. 忽略规则不生效

检查是否有更具体的规则覆盖了通用规则：

```bash
# 查看哪个规则匹配了文件
git check-ignore -v filename
```

### 3. 意外忽略了需要的文件

使用 `!` 前缀可以取消忽略：

```bash
# 忽略所有.log文件，但保留important.log
*.log
!important.log
```

## 📋 维护检查清单

定期检查.gitignore文件的有效性：

- [ ] 检查是否有新的构建产物需要忽略
- [ ] 验证敏感文件是否被正确忽略
- [ ] 确认IDE和编辑器文件被忽略
- [ ] 测试新的文件类型是否需要添加规则
- [ ] 清理不再需要的忽略规则

## 🔍 验证命令

使用以下命令验证.gitignore配置：

```bash
# 查看所有未跟踪的文件
git status --porcelain | grep "^??"

# 查看被忽略的文件
git status --ignored

# 测试特定文件是否被忽略
git check-ignore test.log config_secret.yaml

# 查看.gitignore规则匹配详情
git check-ignore -v build/ logs/ *.tar.gz
```

## 📚 参考资源

- [Git官方文档 - gitignore](https://git-scm.com/docs/gitignore)
- [GitHub gitignore模板](https://github.com/github/gitignore)
- [gitignore.io - 在线生成器](https://www.toptal.com/developers/gitignore)

---

**最后更新**: 2025-07-24  
**维护者**: Traffic Mirror Team
