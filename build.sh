#!/bin/bash

# 流量镜像工具构建和打包脚本
# 用途：构建多平台二进制文件并打包成发行版

set -e

# 配置变量
APP_NAME="traffic-mirror"
VERSION=${VERSION:-"1.0.0"}
BUILD_TIME=$(date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
GO_VERSION=$(go version | awk '{print $3}')

# 构建信息
LDFLAGS="-X main.Version=${VERSION} -X main.BuildTime=${BUILD_TIME} -X main.GitCommit=${GIT_COMMIT} -X main.GoVersion=${GO_VERSION}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    rm -rf build/temp
}

# 设置清理陷阱
trap cleanup EXIT

# 检查环境
check_environment() {
    log_step "检查构建环境..."
    
    # 检查Go版本
    if ! command -v go &> /dev/null; then
        log_error "未找到Go编译器"
        exit 1
    fi
    
    local go_version=$(go version | grep -o 'go[0-9]\+\.[0-9]\+' | sed 's/go//')
    log_info "Go版本: $go_version"
    
    # 检查Git
    if ! command -v git &> /dev/null; then
        log_warn "未找到Git，将使用默认版本信息"
    fi
    
    # 检查源码文件
    if [ ! -f "main.go" ]; then
        log_error "未找到main.go文件，请在项目根目录运行此脚本"
        exit 1
    fi
    
    log_info "环境检查通过"
}

# 创建构建目录
create_build_dirs() {
    log_step "创建构建目录..."
    
    rm -rf build
    mkdir -p build/{temp,dist,packages}
    
    log_info "构建目录创建完成"
}

# 安装依赖
install_dependencies() {
    log_step "安装依赖..."
    
    go mod tidy
    go mod download
    
    log_info "依赖安装完成"
}

# 运行测试
run_tests() {
    log_step "运行测试..."

    # 检查是否有正式的测试文件
    if find . -name "*_test.go" -not -path "./test/*" | grep -q .; then
        log_info "发现测试文件，运行测试..."
        go test $(go list ./... | grep -v '/test') -v
    else
        log_warn "未发现正式测试文件，跳过测试"
        log_info "注意：test目录包含独立的测试程序，不是Go测试文件"
    fi

    log_info "测试完成"
}

# 构建二进制文件
build_binaries() {
    log_step "构建二进制文件..."
    
    # 支持的平台
    platforms=(
        "linux/amd64"
        "linux/arm64"
        "darwin/amd64"
        "darwin/arm64"
        "windows/amd64"
    )
    
    for platform in "${platforms[@]}"; do
        IFS='/' read -r os arch <<< "$platform"
        
        log_info "构建 ${os}/${arch}..."
        
        output_name="${APP_NAME}"
        if [ "$os" = "windows" ]; then
            output_name="${APP_NAME}.exe"
        fi
        
        output_path="build/dist/${os}-${arch}/${output_name}"
        mkdir -p "$(dirname "$output_path")"
        
        CGO_ENABLED=0 GOOS=$os GOARCH=$arch go build \
            -ldflags "$LDFLAGS" \
            -o "$output_path" \
            .
        
        if [ $? -eq 0 ]; then
            log_info "✓ ${os}/${arch} 构建成功"
        else
            log_error "✗ ${os}/${arch} 构建失败"
            exit 1
        fi
    done
    
    log_info "所有平台构建完成"
}

# 创建发行包
create_packages() {
    log_step "创建发行包..."
    
    # 为每个平台创建发行包
    for platform_dir in build/dist/*/; do
        if [ -d "$platform_dir" ]; then
            platform=$(basename "$platform_dir")
            log_info "创建 $platform 发行包..."
            
            package_dir="build/temp/${APP_NAME}-${VERSION}-${platform}"
            mkdir -p "$package_dir"
            
            # 复制二进制文件
            cp "$platform_dir"/* "$package_dir/"
            
            # 复制配置文件
            cp config_prod.yaml "$package_dir/config.yaml"
            cp config_dev.yaml "$package_dir/"
            
            # 复制脚本文件
            cp deploy.sh "$package_dir/"
            cp quick-deploy.sh "$package_dir/"
            cp check_status.sh "$package_dir/"
            
            # 复制systemd服务文件
            cp traffic-mirror.service "$package_dir/"
            
            # 复制文档
            cp README.md "$package_dir/"
            cp USAGE.md "$package_dir/"
            
            # 创建目录结构
            mkdir -p "$package_dir/logs"
            
            # 创建版本信息文件
            cat > "$package_dir/VERSION" << EOF
Application: $APP_NAME
Version: $VERSION
Build Time: $BUILD_TIME
Git Commit: $GIT_COMMIT
Go Version: $GO_VERSION
Platform: $platform
EOF
            
            # 创建安装说明
            create_install_guide "$package_dir" "$platform"
            
            # 打包
            cd build/temp
            if [[ "$platform" == *"windows"* ]]; then
                if command -v zip &> /dev/null; then
                    zip -r "../packages/${APP_NAME}-${VERSION}-${platform}.zip" "${APP_NAME}-${VERSION}-${platform}"
                else
                    log_warn "zip命令未找到，使用tar打包Windows版本"
                    tar -czf "../packages/${APP_NAME}-${VERSION}-${platform}.tar.gz" "${APP_NAME}-${VERSION}-${platform}"
                fi
            else
                tar -czf "../packages/${APP_NAME}-${VERSION}-${platform}.tar.gz" "${APP_NAME}-${VERSION}-${platform}"
            fi
            cd - > /dev/null
            
            log_info "✓ $platform 发行包创建完成"
        fi
    done
    
    log_info "所有发行包创建完成"
}

# 创建安装指南
create_install_guide() {
    local package_dir=$1
    local platform=$2
    
    cat > "$package_dir/INSTALL.md" << 'EOF'
# 流量镜像工具安装指南

## 快速安装

### Linux系统（推荐）

```bash
# 1. 解压安装包
tar -xzf traffic-mirror-*.tar.gz
cd traffic-mirror-*

# 2. 一键部署（需要root权限）
sudo ./deploy.sh

# 3. 验证安装
./check_status.sh
```

### 手动安装

```bash
# 1. 创建用户和目录
sudo useradd -r -s /bin/false traffic-mirror
sudo mkdir -p /opt/traffic-mirror/logs
sudo chown -R traffic-mirror:traffic-mirror /opt/traffic-mirror

# 2. 复制文件
sudo cp traffic-mirror /opt/traffic-mirror/
sudo cp config.yaml /opt/traffic-mirror/
sudo chmod +x /opt/traffic-mirror/traffic-mirror

# 3. 安装systemd服务
sudo cp traffic-mirror.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable traffic-mirror

# 4. 配置文件
sudo vim /opt/traffic-mirror/config.yaml
# 修改Kafka地址和目标API地址

# 5. 启动服务
sudo systemctl start traffic-mirror
```

## 配置说明

请根据您的环境修改 `config.yaml` 文件：

- `kafka.brokers`: Kafka集群地址
- `kafka.topic`: 消费的topic名称
- `target_api.url`: 目标API地址

详细配置说明请参考 `USAGE.md` 文件。

## 验证安装

```bash
# 检查服务状态
sudo systemctl status traffic-mirror

# 查看日志
sudo journalctl -u traffic-mirror -f

# 运行健康检查
./check_status.sh
```

## 故障排查

如遇问题，请参考：
1. `USAGE.md` - 详细使用指南
2. `README.md` - 项目说明
3. 日志文件：`/opt/traffic-mirror/logs/`

## 技术支持

- GitHub Issues: 提交问题和建议
- 文档: 查看完整文档
- 邮件支持: 联系技术团队
EOF
}

# 生成校验和
generate_checksums() {
    log_step "生成校验和..."
    
    cd build/packages
    
    # 生成SHA256校验和
    for file in *; do
        if [ -f "$file" ]; then
            sha256sum "$file" >> "${APP_NAME}-${VERSION}-checksums.txt"
        fi
    done
    
    cd - > /dev/null
    
    log_info "校验和生成完成"
}

# 显示构建结果
show_build_results() {
    log_step "构建结果"
    
    echo
    echo "构建信息："
    echo "  应用名称: $APP_NAME"
    echo "  版本: $VERSION"
    echo "  构建时间: $BUILD_TIME"
    echo "  Git提交: $GIT_COMMIT"
    echo "  Go版本: $GO_VERSION"
    echo
    
    echo "发行包："
    ls -la build/packages/
    echo
    
    echo "安装说明："
    echo "1. 选择适合您系统的发行包"
    echo "2. 解压到目标服务器"
    echo "3. 运行 sudo ./deploy.sh 进行一键部署"
    echo "4. 或参考 INSTALL.md 进行手动安装"
    echo
    
    log_info "构建完成！发行包位于 build/packages/ 目录"
}

# 主函数
main() {
    log_info "开始构建 $APP_NAME v$VERSION..."
    
    check_environment
    create_build_dirs
    install_dependencies
    run_tests
    build_binaries
    create_packages
    generate_checksums
    show_build_results
    
    log_info "构建流程完成！"
}

# 执行主函数
main "$@"
