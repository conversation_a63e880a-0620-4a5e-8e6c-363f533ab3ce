# 开发环境流量镜像工具配置文件
kafka:
  # Kafka集群地址
  brokers:
    - "10.101.1.105:19092"
  # 消费的topic名称
  topic: "lynxiao_flow"
  # 消费者组ID
  group_id: "traffic-mirror-consumer-dev"
  # 消费者配置
  consumer:
    # 从最新位置开始消费（不消费历史消息）
    offset_initial: "newest"
    # 会话超时时间（秒）
    session_timeout: 30
    # 心跳间隔（秒）
    heartbeat_interval: 3

# 目标API配置
target_api:
  # 目标API地址
  url: "http://0.0.0.0:50409/v1/search"
  # 请求超时时间（秒）
  timeout: 30
  # 重试次数
  retry_count: 3
  # 重试间隔（秒）
  retry_interval: 1

# 过滤配置
filter:
  # 需要处理的消息类型
  message_type: "SearchAPI-Request"

# 开发环境日志配置 - 所有日志都输出到控制台便于调试
log:
  # 全局日志级别
  level: "debug"
  # 日志格式: json, text
  format: "text"
  
  # 请求日志配置 - 开发环境输出到控制台
  request:
    enabled: true
    output: "stdout"
    file_path: "./logs/request.log"
    level: "debug"
  
  # 统计日志配置 - 输出到控制台
  stats:
    enabled: true
    output: "stdout"
    level: "info"
  
  # 错误日志配置 - 输出到控制台
  error:
    enabled: true
    output: "stdout"
    file_path: "./logs/error.log"
    level: "warn"
  
  # 系统日志配置 - 输出到控制台
  system:
    enabled: true
    output: "stdout"
    level: "debug"

# 应用配置
app:
  name: "traffic-mirror"
  version: "1.0.0-dev"
  # 启用监控指标
  enable_metrics: true
  # 统计信息输出间隔（秒） - 开发环境更频繁
  metrics_interval: 30
