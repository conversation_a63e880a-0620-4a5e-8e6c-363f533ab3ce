# 模拟API服务器 Dockerfile

FROM golang:1.19-alpine AS builder

WORKDIR /app

# 复制模拟服务器代码
COPY mock_server.go .

# 初始化go module
RUN go mod init mock-server && \
    go mod tidy

# 构建
RUN CGO_ENABLED=0 GOOS=linux go build -o mock-server mock_server.go

# 运行阶段
FROM alpine:latest

RUN apk --no-cache add ca-certificates curl

WORKDIR /app

COPY --from=builder /app/mock-server .

EXPOSE 50409

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:50409/health || exit 1

CMD ["./mock-server"]
