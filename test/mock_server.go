package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

// MockResponse 模拟响应结构
type MockResponse struct {
	Status    string      `json:"status"`
	Message   string      `json:"message"`
	Timestamp int64       `json:"timestamp"`
	Data      interface{} `json:"data,omitempty"`
}

// RequestCounter 请求计数器
var requestCounter = 0

func main() {
	// 设置路由
	http.HandleFunc("/v1/search", handleSearch)
	http.HandleFunc("/health", handleHealth)
	http.HandleFunc("/stats", handleStats)

	fmt.Println("=== Mock Target API Server ===")
	fmt.Println("Starting mock server on :8081")
	fmt.Println("Endpoints:")
	fmt.Println("  POST /v1/search - Main search endpoint")
	fmt.Println("  GET  /health    - Health check")
	fmt.Println("  GET  /stats     - Request statistics")
	fmt.Println()

	// 启动服务器
	log.Fatal(http.ListenAndServe(":8081", nil))
}

// handleSearch 处理搜索请求
func handleSearch(w http.ResponseWriter, r *http.Request) {
	requestCounter++

	// 记录请求信息
	fmt.Printf("[%s] Request #%d: %s %s\n",
		time.Now().Format("15:04:05"), requestCounter, r.Method, r.URL.Path)

	// 检查请求方法
	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 检查Content-Type
	contentType := r.Header.Get("Content-Type")
	if contentType != "application/json" {
		fmt.Printf("  Warning: Content-Type is '%s', expected 'application/json'\n", contentType)
	}

	// 读取请求体
	body, err := io.ReadAll(r.Body)
	if err != nil {
		fmt.Printf("  Error reading request body: %v\n", err)
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	// 解析请求JSON
	var requestData map[string]interface{}
	if err := json.Unmarshal(body, &requestData); err != nil {
		fmt.Printf("  Error parsing JSON: %v\n", err)
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// 提取关键信息
	traceID := ""
	appID := ""
	query := []interface{}{}

	if header, ok := requestData["header"].(map[string]interface{}); ok {
		if tid, ok := header["traceId"].(string); ok {
			traceID = tid
		}
		if aid, ok := header["appId"].(string); ok {
			appID = aid
		}
	}

	if payload, ok := requestData["payload"].(map[string]interface{}); ok {
		if q, ok := payload["query"].([]interface{}); ok {
			query = q
		}
	}

	// 打印请求详情
	fmt.Printf("  Trace ID: %s\n", traceID)
	fmt.Printf("  App ID: %s\n", appID)
	fmt.Printf("  Query: %v\n", query)
	fmt.Printf("  Request size: %d bytes\n", len(body))

	// 模拟处理时间
	time.Sleep(50 * time.Millisecond)

	// 构造响应
	response := MockResponse{
		Status:    "success",
		Message:   "Request processed successfully",
		Timestamp: time.Now().Unix(),
		Data: map[string]interface{}{
			"trace_id":    traceID,
			"app_id":      appID,
			"query_count": len(query),
			"request_id":  fmt.Sprintf("req_%d", requestCounter),
		},
	}

	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("X-Request-ID", fmt.Sprintf("req_%d", requestCounter))

	// 发送响应
	if err := json.NewEncoder(w).Encode(response); err != nil {
		fmt.Printf("  Error encoding response: %v\n", err)
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
		return
	}

	fmt.Printf("  Response sent successfully\n\n")
}

// handleHealth 健康检查
func handleHealth(w http.ResponseWriter, r *http.Request) {
	response := MockResponse{
		Status:    "healthy",
		Message:   "Mock server is running",
		Timestamp: time.Now().Unix(),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleStats 统计信息
func handleStats(w http.ResponseWriter, r *http.Request) {
	response := MockResponse{
		Status:    "success",
		Message:   "Request statistics",
		Timestamp: time.Now().Unix(),
		Data: map[string]interface{}{
			"total_requests": requestCounter,
			"uptime":         "running",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}
