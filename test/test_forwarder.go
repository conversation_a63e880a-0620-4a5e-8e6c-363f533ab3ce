package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"

	"traffic-mirror/config"
	"traffic-mirror/forwarder"
	"traffic-mirror/logger"
	"traffic-mirror/message"
)

func main() {
	fmt.Println("=== Testing HTTP Forwarder ===")

	// 加载配置
	cfg, err := config.LoadConfig("../test_config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 设置日志
	log, err := logger.SetupLogger(cfg)
	if err != nil {
		log.Fatalf("Failed to setup logger: %v", err)
	}

	// 创建消息解析器
	parser := message.NewParser(log, "SearchAPI-Request")

	// 创建HTTP转发器
	httpForwarder := forwarder.NewHTTPForwarder(cfg, log)

	// 读取测试数据
	data, err := os.ReadFile("../demo_test.json")
	if err != nil {
		log.Fatalf("Failed to read demo_test.json: %v", err)
	}

	fmt.Printf("Input data size: %d bytes\n", len(data))

	// 解析消息
	request, err := parser.ProcessMessage(data)
	if err != nil {
		log.Fatalf("Failed to process message: %v", err)
	}

	if request == nil {
		fmt.Println("Message was skipped (not target type)")
		return
	}

	fmt.Println("\n=== Parsed Request ===")
	requestJSON, _ := json.MarshalIndent(request, "", "  ")
	fmt.Printf("%s\n", string(requestJSON))

	fmt.Println("\n=== Forwarding Request ===")
	fmt.Printf("Target URL: %s\n", cfg.TargetAPI.URL)

	// 转发请求
	if err := httpForwarder.ForwardRequest(request); err != nil {
		log.Fatalf("Failed to forward request: %v", err)
	}

	fmt.Println("Request forwarded successfully!")

	// 获取转发器统计信息
	metrics := httpForwarder.GetMetrics()
	fmt.Println("\n=== Forwarder Metrics ===")
	fmt.Printf("Total Requests: %d\n", metrics.TotalRequests)
	fmt.Printf("Success Requests: %d\n", metrics.SuccessRequests)
	fmt.Printf("Failed Requests: %d\n", metrics.FailedRequests)
	fmt.Printf("Total Retries: %d\n", metrics.TotalRetries)
	fmt.Printf("Average Latency: %dms\n", metrics.AverageLatency)
	fmt.Printf("Success Rate: %.2f%%\n", httpForwarder.GetSuccessRate())

	// 关闭转发器
	httpForwarder.Close()
}
