#!/bin/bash

# 流量镜像工具发布脚本
# 用途：自动化版本发布流程

set -e

# 配置变量
APP_NAME="traffic-mirror"
RELEASE_DIR="releases"
CURRENT_VERSION=""
NEW_VERSION=""

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
流量镜像工具发布脚本

用法: $0 [选项] <版本号>

选项:
  -h, --help          显示此帮助信息
  -p, --patch         自动递增补丁版本号
  -m, --minor         自动递增次版本号
  -M, --major         自动递增主版本号
  --dry-run          预览模式，不执行实际操作
  --skip-tests       跳过测试
  --skip-build       跳过构建（仅更新版本）

示例:
  $0 1.2.3           发布指定版本
  $0 --patch         自动递增补丁版本 (1.0.0 -> 1.0.1)
  $0 --minor         自动递增次版本 (1.0.0 -> 1.1.0)
  $0 --major         自动递增主版本 (1.0.0 -> 2.0.0)
  $0 --dry-run 1.2.3 预览发布过程
EOF
}

# 解析命令行参数
parse_args() {
    local auto_increment=""
    local dry_run=false
    local skip_tests=false
    local skip_build=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -p|--patch)
                auto_increment="patch"
                shift
                ;;
            -m|--minor)
                auto_increment="minor"
                shift
                ;;
            -M|--major)
                auto_increment="major"
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            --skip-tests)
                skip_tests=true
                shift
                ;;
            --skip-build)
                skip_build=true
                shift
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [ -z "$NEW_VERSION" ]; then
                    NEW_VERSION="$1"
                else
                    log_error "只能指定一个版本号"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 导出变量供其他函数使用
    export DRY_RUN=$dry_run
    export SKIP_TESTS=$skip_tests
    export SKIP_BUILD=$skip_build
    export AUTO_INCREMENT=$auto_increment
}

# 获取当前版本
get_current_version() {
    log_step "获取当前版本..."
    
    # 尝试从Git标签获取
    if command -v git &> /dev/null && git rev-parse --git-dir > /dev/null 2>&1; then
        CURRENT_VERSION=$(git describe --tags --abbrev=0 2>/dev/null | sed 's/^v//' || echo "0.0.0")
    else
        # 从VERSION文件获取
        if [ -f "VERSION" ]; then
            CURRENT_VERSION=$(cat VERSION | tr -d '\n')
        else
            CURRENT_VERSION="0.0.0"
        fi
    fi
    
    log_info "当前版本: $CURRENT_VERSION"
}

# 计算新版本号
calculate_new_version() {
    if [ -n "$AUTO_INCREMENT" ]; then
        log_step "自动计算新版本号..."
        
        IFS='.' read -r major minor patch <<< "$CURRENT_VERSION"
        
        case $AUTO_INCREMENT in
            "patch")
                NEW_VERSION="$major.$minor.$((patch + 1))"
                ;;
            "minor")
                NEW_VERSION="$major.$((minor + 1)).0"
                ;;
            "major")
                NEW_VERSION="$((major + 1)).0.0"
                ;;
        esac
        
        log_info "新版本号: $NEW_VERSION"
    elif [ -z "$NEW_VERSION" ]; then
        log_error "请指定版本号或使用自动递增选项"
        show_help
        exit 1
    fi
}

# 验证版本号格式
validate_version() {
    log_step "验证版本号格式..."
    
    if ! [[ $NEW_VERSION =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        log_error "版本号格式错误，应为 x.y.z 格式"
        exit 1
    fi
    
    # 检查版本号是否递增
    if [ "$NEW_VERSION" = "$CURRENT_VERSION" ]; then
        log_error "新版本号不能与当前版本相同"
        exit 1
    fi
    
    log_info "版本号验证通过: $NEW_VERSION"
}

# 检查工作目录状态
check_working_directory() {
    log_step "检查工作目录状态..."
    
    if command -v git &> /dev/null && git rev-parse --git-dir > /dev/null 2>&1; then
        # 检查是否有未提交的更改
        if ! git diff-index --quiet HEAD --; then
            log_warn "工作目录有未提交的更改"
            if [ "$DRY_RUN" = false ]; then
                read -p "是否继续？(y/N): " -n 1 -r
                echo
                if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                    log_info "发布已取消"
                    exit 0
                fi
            fi
        fi
        
        # 检查是否在主分支
        current_branch=$(git branch --show-current)
        if [ "$current_branch" != "main" ] && [ "$current_branch" != "master" ]; then
            log_warn "当前不在主分支 (当前: $current_branch)"
        fi
    fi
    
    log_info "工作目录检查完成"
}

# 更新版本信息
update_version_info() {
    log_step "更新版本信息..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] 将更新版本信息到 $NEW_VERSION"
        return
    fi
    
    # 更新VERSION文件
    echo "$NEW_VERSION" > VERSION
    
    # 更新main.go中的版本信息（如果存在）
    if [ -f "main.go" ] && grep -q "Version.*=" main.go; then
        sed -i.bak "s/Version.*=.*/Version = \"$NEW_VERSION\"/" main.go
        rm -f main.go.bak
    fi
    
    log_info "版本信息已更新"
}

# 运行测试
run_tests() {
    if [ "$SKIP_TESTS" = true ]; then
        log_warn "跳过测试"
        return
    fi
    
    log_step "运行测试..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] 将运行测试"
        return
    fi
    
    if [ -d "test" ] || ls *_test.go 1> /dev/null 2>&1; then
        go test ./... -v
        log_info "测试通过"
    else
        log_warn "未发现测试文件"
    fi
}

# 构建发行包
build_release() {
    if [ "$SKIP_BUILD" = true ]; then
        log_warn "跳过构建"
        return
    fi
    
    log_step "构建发行包..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] 将构建版本 $NEW_VERSION"
        return
    fi
    
    # 设置版本环境变量并运行构建脚本
    VERSION="$NEW_VERSION" ./build.sh
    
    log_info "构建完成"
}

# 创建Git标签
create_git_tag() {
    if ! command -v git &> /dev/null || ! git rev-parse --git-dir > /dev/null 2>&1; then
        log_warn "不是Git仓库，跳过标签创建"
        return
    fi
    
    log_step "创建Git标签..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] 将创建标签 v$NEW_VERSION"
        return
    fi
    
    # 提交版本更改
    git add VERSION
    if [ -f "main.go" ]; then
        git add main.go
    fi
    git commit -m "Release version $NEW_VERSION" || true
    
    # 创建标签
    git tag -a "v$NEW_VERSION" -m "Release version $NEW_VERSION"
    
    log_info "Git标签 v$NEW_VERSION 已创建"
}

# 移动发行包到releases目录
organize_releases() {
    log_step "整理发行包..."
    
    if [ "$DRY_RUN" = true ]; then
        log_info "[DRY RUN] 将整理发行包到 $RELEASE_DIR/$NEW_VERSION/"
        return
    fi
    
    # 创建releases目录结构
    mkdir -p "$RELEASE_DIR/$NEW_VERSION"
    
    # 移动构建产物
    if [ -d "build/packages" ]; then
        cp -r build/packages/* "$RELEASE_DIR/$NEW_VERSION/"
    fi
    
    # 创建发布说明模板
    create_release_notes
    
    log_info "发行包已整理到 $RELEASE_DIR/$NEW_VERSION/"
}

# 创建发布说明
create_release_notes() {
    local release_notes_file="$RELEASE_DIR/$NEW_VERSION/RELEASE_NOTES.md"
    
    cat > "$release_notes_file" << EOF
# Traffic Mirror v$NEW_VERSION 发布说明

## 📦 下载

选择适合您系统的安装包：

- **Linux x64**: \`traffic-mirror-$NEW_VERSION-linux-amd64.tar.gz\`
- **Linux ARM64**: \`traffic-mirror-$NEW_VERSION-linux-arm64.tar.gz\`
- **macOS x64**: \`traffic-mirror-$NEW_VERSION-darwin-amd64.tar.gz\`
- **macOS ARM64**: \`traffic-mirror-$NEW_VERSION-darwin-arm64.tar.gz\`
- **Windows x64**: \`traffic-mirror-$NEW_VERSION-windows-amd64.zip\`

## 🚀 快速安装

### Linux系统（推荐）

\`\`\`bash
# 下载并解压
tar -xzf traffic-mirror-$NEW_VERSION-linux-amd64.tar.gz
cd traffic-mirror-$NEW_VERSION-linux-amd64

# 一键部署
sudo ./deploy.sh
\`\`\`

### 验证安装

\`\`\`bash
# 检查服务状态
sudo systemctl status traffic-mirror

# 运行健康检查
./check_status.sh
\`\`\`

## 📋 更新内容

### 新功能
- [ ] 功能1描述
- [ ] 功能2描述

### 改进
- [ ] 改进1描述
- [ ] 改进2描述

### 修复
- [ ] 修复1描述
- [ ] 修复2描述

## 🔧 升级指南

从旧版本升级：

\`\`\`bash
# 停止服务
sudo systemctl stop traffic-mirror

# 备份配置
sudo cp /opt/traffic-mirror/config.yaml /opt/traffic-mirror/config.yaml.backup

# 安装新版本
sudo cp traffic-mirror /opt/traffic-mirror/

# 启动服务
sudo systemctl start traffic-mirror
\`\`\`

## 📞 技术支持

- 📖 [使用指南](USAGE.md)
- 🐛 [问题反馈](https://github.com/your-org/traffic-mirror/issues)
- 💬 技术交流群

## 🔐 校验和

请使用以下命令验证下载文件的完整性：

\`\`\`bash
sha256sum -c traffic-mirror-$NEW_VERSION-checksums.txt
\`\`\`

---

**发布时间**: $(date -u '+%Y-%m-%d %H:%M:%S UTC')  
**发布版本**: v$NEW_VERSION
EOF

    log_info "发布说明已创建: $release_notes_file"
}

# 显示发布结果
show_release_results() {
    log_step "发布结果"
    
    echo
    echo "🎉 版本 $NEW_VERSION 发布完成！"
    echo
    echo "📦 发行包位置: $RELEASE_DIR/$NEW_VERSION/"
    echo "📋 发布说明: $RELEASE_DIR/$NEW_VERSION/RELEASE_NOTES.md"
    echo
    
    if [ -d "$RELEASE_DIR/$NEW_VERSION" ]; then
        echo "📁 发行包列表:"
        ls -la "$RELEASE_DIR/$NEW_VERSION/"
        echo
    fi
    
    echo "🚀 后续步骤:"
    echo "1. 编辑发布说明: $RELEASE_DIR/$NEW_VERSION/RELEASE_NOTES.md"
    echo "2. 测试发行包安装"
    echo "3. 推送Git标签: git push origin v$NEW_VERSION"
    echo "4. 发布到GitHub/GitLab Releases"
    echo "5. 更新文档和通知用户"
    echo
}

# 主函数
main() {
    log_info "开始发布流程..."
    
    parse_args "$@"
    get_current_version
    calculate_new_version
    validate_version
    check_working_directory
    
    if [ "$DRY_RUN" = true ]; then
        log_warn "预览模式 - 不会执行实际操作"
    fi
    
    update_version_info
    run_tests
    build_release
    create_git_tag
    organize_releases
    show_release_results
    
    log_info "发布流程完成！"
}

# 执行主函数
main "$@"
