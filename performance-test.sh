#!/bin/bash

# 流量镜像工具性能测试脚本

set -e

# 配置变量
APP_NAME="traffic-mirror"
TEST_DURATION=60
LOG_FILE="performance_test.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_result() {
    echo -e "${CYAN}[RESULT]${NC} $1"
}

# 显示横幅
show_banner() {
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                    流量镜像工具性能测试                        ║
║                    Performance Test Suite                   ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo
}

# 显示帮助信息
show_help() {
    cat << EOF
流量镜像工具性能测试脚本

用法: $0 [选项]

选项:
  -h, --help              显示此帮助信息
  -d, --duration SECONDS  测试持续时间 (默认: 60秒)
  -w, --workers NUMBER    并行工作数 (默认: 测试多个值)
  -t, --timeout SECONDS   API超时时间 (默认: 测试多个值)
  --single-test          只运行一次测试
  --kafka-broker BROKER   指定Kafka地址
  --kafka-topic TOPIC     指定Kafka主题

示例:
  $0                                    # 运行完整性能测试套件
  $0 --duration 30 --workers 20       # 30秒测试，20个worker
  $0 --single-test --workers 50       # 单次测试，50个worker

EOF
}

# 解析命令行参数
parse_args() {
    local single_test=false
    local workers=""
    local timeout=""
    local kafka_broker="10.101.1.105:19092"
    local kafka_topic="lynxiao_flow"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--duration)
                TEST_DURATION="$2"
                shift 2
                ;;
            -w|--workers)
                workers="$2"
                shift 2
                ;;
            -t|--timeout)
                timeout="$2"
                shift 2
                ;;
            --single-test)
                single_test=true
                shift
                ;;
            --kafka-broker)
                kafka_broker="$2"
                shift 2
                ;;
            --kafka-topic)
                kafka_topic="$2"
                shift 2
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 导出变量
    export SINGLE_TEST=$single_test
    export WORKERS=$workers
    export TIMEOUT=$timeout
    export KAFKA_BROKER=$kafka_broker
    export KAFKA_TOPIC=$kafka_topic
}

# 检查环境
check_environment() {
    log_step "检查测试环境..."
    
    if [ ! -f "$APP_NAME" ]; then
        log_error "应用程序不存在: $APP_NAME"
        log_info "请先构建应用程序: go build -o $APP_NAME ."
        exit 1
    fi
    
    if [ ! -f "config_simple.yaml" ]; then
        log_error "配置文件不存在: config_simple.yaml"
        exit 1
    fi
    
    log_info "环境检查通过"
}

# 运行单次性能测试
run_performance_test() {
    local workers=$1
    local timeout=$2
    local test_name=$3
    
    log_step "运行性能测试: $test_name"
    log_info "配置: Workers=$workers, Timeout=${timeout}s, Duration=${TEST_DURATION}s"
    
    # 生成唯一的消费者组ID
    local group_id="traffic-mirror-perf-$(date +%s)-$workers"
    
    # 设置环境变量
    export KAFKA_BROKERS=$KAFKA_BROKER
    export KAFKA_TOPIC=$KAFKA_TOPIC
    export KAFKA_GROUP_ID=$group_id
    export KAFKA_PARALLEL_WORKERS=$workers
    export TARGET_API_URL=http://localhost:50409/v1/search
    export TARGET_API_TIMEOUT=$timeout
    export MESSAGE_TYPE=SearchAPI-Request
    export LOG_LEVEL=warn
    
    # 运行测试
    local start_time=$(date +%s)
    timeout $TEST_DURATION ./$APP_NAME -config config_simple.yaml > "$LOG_FILE" 2>&1 || true
    local end_time=$(date +%s)
    local actual_duration=$((end_time - start_time))
    
    # 解析结果
    local stats_line=$(grep "Traffic mirror statistics" "$LOG_FILE" | tail -1)
    if [ -n "$stats_line" ]; then
        # 提取关键指标
        local total_messages=$(echo "$stats_line" | grep -o '"total_messages":[0-9]*' | cut -d':' -f2)
        local processed_messages=$(echo "$stats_line" | grep -o '"processed_messages":[0-9]*' | cut -d':' -f2)
        local processing_rate=$(echo "$stats_line" | grep -o '"processing_rate":"[^"]*"' | cut -d'"' -f4)
        local success_rate=$(echo "$stats_line" | grep -o '"success_rate":"[^"]*"' | cut -d'"' -f4)
        
        # 显示结果
        log_result "测试结果 - $test_name:"
        echo "  持续时间: ${actual_duration}s"
        echo "  总消息数: ${total_messages:-0}"
        echo "  处理消息数: ${processed_messages:-0}"
        echo "  处理速率: ${processing_rate:-0}"
        echo "  成功率: ${success_rate:-0}"
        echo "  并行工作数: $workers"
        echo "  API超时: ${timeout}s"
        echo
        
        # 记录到CSV文件
        echo "$test_name,$workers,$timeout,$actual_duration,${total_messages:-0},${processed_messages:-0},${processing_rate:-0},${success_rate:-0}" >> performance_results.csv
    else
        log_warn "无法解析测试结果"
    fi
}

# 运行完整性能测试套件
run_test_suite() {
    log_step "运行完整性能测试套件"
    
    # 创建CSV结果文件
    echo "Test Name,Workers,Timeout,Duration,Total Messages,Processed Messages,Processing Rate,Success Rate" > performance_results.csv
    
    # 测试不同的worker数量
    local worker_configs=(10 20 30 50 100)
    local timeout_configs=(2 3 5)
    
    for workers in "${worker_configs[@]}"; do
        for timeout in "${timeout_configs[@]}"; do
            local test_name="Workers-${workers}_Timeout-${timeout}s"
            run_performance_test $workers $timeout "$test_name"
            
            # 等待一段时间让系统稳定
            sleep 5
        done
    done
}

# 显示测试结果摘要
show_results_summary() {
    log_step "性能测试结果摘要"
    
    if [ -f "performance_results.csv" ]; then
        echo
        log_result "最佳性能配置:"
        
        # 找出处理速率最高的配置
        local best_rate_line=$(tail -n +2 performance_results.csv | sort -t',' -k7 -nr | head -1)
        if [ -n "$best_rate_line" ]; then
            local test_name=$(echo "$best_rate_line" | cut -d',' -f1)
            local workers=$(echo "$best_rate_line" | cut -d',' -f2)
            local timeout=$(echo "$best_rate_line" | cut -d',' -f3)
            local rate=$(echo "$best_rate_line" | cut -d',' -f7)
            
            echo "  配置: $test_name"
            echo "  并行工作数: $workers"
            echo "  API超时: ${timeout}s"
            echo "  处理速率: $rate"
        fi
        
        echo
        log_result "推荐生产环境配置:"
        echo "  KAFKA_PARALLEL_WORKERS=$workers"
        echo "  TARGET_API_TIMEOUT=$timeout"
        echo "  LOG_LEVEL=warn"
        
        echo
        log_info "详细结果已保存到: performance_results.csv"
    fi
}

# 主函数
main() {
    show_banner
    parse_args "$@"
    check_environment
    
    if [ "$SINGLE_TEST" = true ]; then
        local workers=${WORKERS:-20}
        local timeout=${TIMEOUT:-3}
        run_performance_test $workers $timeout "Single-Test"
    else
        run_test_suite
        show_results_summary
    fi
    
    log_info "性能测试完成！"
}

# 执行主函数
main "$@"
