# 流量镜像工具发布指南

本文档描述了如何构建、打包和发布流量镜像工具的完整流程。

## 📋 目录

- [快速发布](#快速发布)
- [构建系统](#构建系统)
- [发布流程](#发布流程)
- [部署包结构](#部署包结构)
- [运维部署](#运维部署)
- [Docker部署](#docker部署)
- [CI/CD集成](#cicd集成)

## 🚀 快速发布

### 一键构建和发布

```bash
# 1. 构建所有平台的发行包
make package

# 2. 发布新的补丁版本 (1.0.0 -> 1.0.1)
make release-patch

# 3. 发布新的次版本 (1.0.0 -> 1.1.0)
make release-minor

# 4. 发布指定版本
make release VERSION=1.2.3
```

### 预览发布过程

```bash
# 预览发布过程，不执行实际操作
make release-dry-run
```

## 🔧 构建系统

### 可用的Make命令

```bash
# 查看所有可用命令
make help

# 开发相关
make deps          # 安装依赖
make build         # 构建本地平台
make test          # 运行测试
make dev           # 开发模式运行

# 构建相关
make build-all     # 构建所有平台
make package       # 创建发行包
make clean         # 清理构建文件

# 部署相关
make install       # 本地安装
make uninstall     # 卸载应用
make status        # 检查状态

# Docker相关
make docker-build  # 构建Docker镜像
make docker-run    # 运行Docker容器
```

### 手动构建流程

```bash
# 1. 安装依赖
go mod tidy

# 2. 运行测试
go test ./...

# 3. 构建所有平台
./build.sh

# 4. 查看构建结果
ls -la build/packages/
```

## 📦 发布流程

### 自动化发布

使用发布脚本进行版本管理：

```bash
# 发布补丁版本
./release.sh --patch

# 发布次版本
./release.sh --minor

# 发布主版本
./release.sh --major

# 发布指定版本
./release.sh 1.2.3

# 预览发布（不执行实际操作）
./release.sh --dry-run 1.2.3
```

### 手动发布流程

```bash
# 1. 更新版本号
echo "1.2.3" > VERSION

# 2. 构建发行包
VERSION=1.2.3 ./build.sh

# 3. 创建Git标签
git add VERSION
git commit -m "Release version 1.2.3"
git tag -a "v1.2.3" -m "Release version 1.2.3"

# 4. 推送到远程仓库
git push origin main
git push origin v1.2.3
```

## 📁 部署包结构

每个发行包包含以下文件：

```
traffic-mirror-1.0.0-linux-amd64/
├── traffic-mirror              # 主程序
├── config.yaml                 # 生产配置文件
├── config_dev.yaml            # 开发配置文件
├── deploy.sh                  # 原始部署脚本
├── quick-deploy.sh            # 一键部署脚本
├── check_status.sh            # 状态检查脚本
├── traffic-mirror.service     # systemd服务文件
├── README.md                  # 项目说明
├── USAGE.md                   # 使用指南
├── INSTALL.md                 # 安装指南
├── VERSION                    # 版本信息
└── logs/                      # 日志目录
```

### 发行包命名规则

```
traffic-mirror-{版本号}-{操作系统}-{架构}.{扩展名}

示例:
- traffic-mirror-1.0.0-linux-amd64.tar.gz
- traffic-mirror-1.0.0-linux-arm64.tar.gz
- traffic-mirror-1.0.0-darwin-amd64.tar.gz
- traffic-mirror-1.0.0-windows-amd64.zip
```

## 🚀 运维部署

### 一键部署（推荐）

运维人员收到发行包后，可以使用一键部署脚本：

```bash
# 1. 解压发行包
tar -xzf traffic-mirror-1.0.0-linux-amd64.tar.gz
cd traffic-mirror-1.0.0-linux-amd64

# 2. 一键部署（需要root权限）
sudo ./quick-deploy.sh

# 3. 验证部署
./check_status.sh
```

### 自定义部署

```bash
# 指定Kafka地址和目标API
sudo ./quick-deploy.sh \
  --kafka-broker "**********:9092" \
  --kafka-topic "my_topic" \
  --target-api "http://api.example.com/v1/search"

# 预览部署过程
sudo ./quick-deploy.sh --dry-run

# 卸载应用
sudo ./quick-deploy.sh --uninstall
```

### 手动部署

如果需要手动部署，请参考 `INSTALL.md` 文件中的详细步骤。

## 🐳 Docker部署

### 构建Docker镜像

```bash
# 构建镜像
make docker-build

# 或手动构建
docker build -t traffic-mirror:1.0.0 .
```

### 使用Docker Compose

```bash
# 启动完整的开发环境（包含Kafka和模拟API）
docker-compose up -d

# 只启动应用
docker-compose up -d traffic-mirror

# 查看日志
docker-compose logs -f traffic-mirror

# 停止服务
docker-compose down
```

### 生产环境Docker部署

```bash
# 1. 准备配置文件
cp config_prod.yaml config.yaml
# 编辑配置文件，设置正确的Kafka地址和目标API

# 2. 运行容器
docker run -d \
  --name traffic-mirror \
  --restart unless-stopped \
  -v $(pwd)/config.yaml:/app/config.yaml:ro \
  -v $(pwd)/logs:/app/logs \
  traffic-mirror:1.0.0

# 3. 检查状态
docker logs traffic-mirror
docker exec traffic-mirror ps aux
```

## 🔄 CI/CD集成

### GitHub Actions示例

```yaml
# .github/workflows/release.yml
name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build-and-release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: 1.19
          
      - name: Build packages
        run: make package
        
      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          files: build/packages/*
          generate_release_notes: true
```

### GitLab CI示例

```yaml
# .gitlab-ci.yml
stages:
  - test
  - build
  - release

test:
  stage: test
  script:
    - make test

build:
  stage: build
  script:
    - make package
  artifacts:
    paths:
      - build/packages/
    expire_in: 1 week

release:
  stage: release
  script:
    - echo "Creating release..."
  only:
    - tags
```

## 📋 发布检查清单

发布前请确认以下事项：

### 代码质量
- [ ] 所有测试通过
- [ ] 代码格式化完成
- [ ] 静态检查通过
- [ ] 安全检查通过

### 文档更新
- [ ] README.md 更新
- [ ] USAGE.md 更新
- [ ] CHANGELOG.md 更新
- [ ] 版本号更新

### 构建测试
- [ ] 所有平台构建成功
- [ ] 发行包完整性检查
- [ ] 部署脚本测试
- [ ] Docker镜像构建成功

### 部署验证
- [ ] 开发环境部署测试
- [ ] 配置文件验证
- [ ] 服务启动测试
- [ ] 健康检查通过

## 🆘 故障排查

### 构建失败

```bash
# 检查Go环境
go version
go env

# 清理并重新构建
make clean
make deps
make build
```

### 部署失败

```bash
# 检查系统要求
./quick-deploy.sh --dry-run

# 查看详细错误
sudo journalctl -u traffic-mirror -f

# 检查配置文件
./traffic-mirror -config config.yaml -validate
```

### Docker问题

```bash
# 检查镜像
docker images | grep traffic-mirror

# 查看容器日志
docker logs traffic-mirror

# 进入容器调试
docker exec -it traffic-mirror sh
```

## 📞 技术支持

如遇问题，请：

1. 查看相关文档（README.md, USAGE.md）
2. 检查日志文件
3. 提交GitHub Issue
4. 联系维护团队

---

**文档版本**: v1.0.0  
**最后更新**: 2025-07-24
