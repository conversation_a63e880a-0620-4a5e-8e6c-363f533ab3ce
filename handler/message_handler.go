package handler

import (
	"fmt"
	"sync/atomic"
	"time"

	"github.com/sirupsen/logrus"

	"traffic-mirror/config"
	"traffic-mirror/forwarder"
	"traffic-mirror/kafka"
	"traffic-mirror/logger"
	"traffic-mirror/message"
)

// MessageHandler 消息处理器
type MessageHandler struct {
	config     *config.Config
	logManager *logger.LoggerManager
	parser     *message.Parser
	forwarder  *forwarder.HTTPForwarder
	metrics    *HandlerMetrics
}

// HandlerMetrics 处理器统计指标
type HandlerMetrics struct {
	TotalMessages     int64 `json:"total_messages"`
	ProcessedMessages int64 `json:"processed_messages"`
	SkippedMessages   int64 `json:"skipped_messages"`
	ErrorMessages     int64 `json:"error_messages"`
	StartTime         int64 `json:"start_time"`
	LastMessageTime   int64 `json:"last_message_time"`
}

// NewMessageHandler 创建新的消息处理器
func NewMessageHandler(cfg *config.Config, logManager *logger.LoggerManager) (*MessageHandler, error) {
	// 创建消息解析器 - 使用请求日志器
	requestLogger := logManager.GetLogger(logger.LogTypeRequest)
	parser := message.NewParser(requestLogger, cfg.Filter.MessageType)

	// 创建HTTP转发器 - 使用请求日志器
	httpForwarder := forwarder.NewHTTPForwarder(cfg, requestLogger)

	handler := &MessageHandler{
		config:     cfg,
		logManager: logManager,
		parser:     parser,
		forwarder:  httpForwarder,
		metrics: &HandlerMetrics{
			StartTime: time.Now().Unix(),
		},
	}

	return handler, nil
}

// HandleMessage 实现kafka.MessageHandler接口
func (h *MessageHandler) HandleMessage(message *kafka.Message) error {
	// 更新统计指标
	atomic.AddInt64(&h.metrics.TotalMessages, 1)
	atomic.StoreInt64(&h.metrics.LastMessageTime, time.Now().Unix())

	// 获取消息基本信息用于日志
	messageInfo := h.parser.GetMessageInfo(message.Value)

	// 记录处理消息的调试日志
	h.logManager.LogRequest(logrus.DebugLevel, logrus.Fields{
		"topic":        message.Topic,
		"partition":    message.Partition,
		"offset":       message.Offset,
		"message_info": messageInfo,
	}, "Processing message")

	// 解析和处理消息
	request, err := h.parser.ProcessMessage(message.Value)
	if err != nil {
		atomic.AddInt64(&h.metrics.ErrorMessages, 1)
		// 解析失败只记录日志，不返回错误，继续处理下一条消息
		h.logManager.LogError(logrus.WarnLevel, logrus.Fields{
			"topic":        message.Topic,
			"partition":    message.Partition,
			"offset":       message.Offset,
			"message_info": messageInfo,
			"error":        err.Error(),
		}, "Failed to process message, skipping")
		return nil
	}

	// 如果返回nil，表示消息被跳过（不是目标类型）
	if request == nil {
		atomic.AddInt64(&h.metrics.SkippedMessages, 1)
		h.logManager.LogRequest(logrus.DebugLevel, logrus.Fields{
			"topic":        message.Topic,
			"partition":    message.Partition,
			"offset":       message.Offset,
			"message_info": messageInfo,
		}, "Message skipped (not target type)")
		return nil
	}

	// 转发请求 (Fire-and-Forget模式，不检查错误)
	h.forwarder.ForwardRequest(request)

	atomic.AddInt64(&h.metrics.ProcessedMessages, 1)
	h.logManager.LogRequest(logrus.DebugLevel, logrus.Fields{
		"topic":     message.Topic,
		"partition": message.Partition,
		"offset":    message.Offset,
		"trace_id":  request.Header.TraceID,
		"app_id":    request.Header.AppID,
		"query":     request.Payload.Query,
	}, "Message forwarded (fire-and-forget)")

	return nil
}

// GetMetrics 获取处理器统计指标
func (h *MessageHandler) GetMetrics() *HandlerMetrics {
	return &HandlerMetrics{
		TotalMessages:     atomic.LoadInt64(&h.metrics.TotalMessages),
		ProcessedMessages: atomic.LoadInt64(&h.metrics.ProcessedMessages),
		SkippedMessages:   atomic.LoadInt64(&h.metrics.SkippedMessages),
		ErrorMessages:     atomic.LoadInt64(&h.metrics.ErrorMessages),
		StartTime:         h.metrics.StartTime,
		LastMessageTime:   atomic.LoadInt64(&h.metrics.LastMessageTime),
	}
}

// GetForwarderMetrics 获取转发器统计指标
func (h *MessageHandler) GetForwarderMetrics() *forwarder.Metrics {
	return h.forwarder.GetMetrics()
}

// GetProcessingRate 获取处理速率（消息/秒）
func (h *MessageHandler) GetProcessingRate() float64 {
	metrics := h.GetMetrics()
	duration := time.Now().Unix() - metrics.StartTime
	if duration == 0 {
		return 0.0
	}
	return float64(metrics.TotalMessages) / float64(duration)
}

// GetSuccessRate 获取成功率
func (h *MessageHandler) GetSuccessRate() float64 {
	metrics := h.GetMetrics()
	if metrics.TotalMessages == 0 {
		return 0.0
	}
	return float64(metrics.ProcessedMessages) / float64(metrics.TotalMessages) * 100
}

// PrintStats 打印统计信息
func (h *MessageHandler) PrintStats() {
	handlerMetrics := h.GetMetrics()
	forwarderMetrics := h.GetForwarderMetrics()

	h.logManager.LogStats(logrus.InfoLevel, logrus.Fields{
		"handler_metrics": map[string]interface{}{
			"total_messages":     handlerMetrics.TotalMessages,
			"processed_messages": handlerMetrics.ProcessedMessages,
			"skipped_messages":   handlerMetrics.SkippedMessages,
			"error_messages":     handlerMetrics.ErrorMessages,
			"processing_rate":    fmt.Sprintf("%.2f msg/s", h.GetProcessingRate()),
			"success_rate":       fmt.Sprintf("%.2f%%", h.GetSuccessRate()),
		},
		"forwarder_metrics": map[string]interface{}{
			"total_requests":   forwarderMetrics.TotalRequests,
			"success_requests": forwarderMetrics.SuccessRequests,
			"failed_requests":  forwarderMetrics.FailedRequests,
			"total_retries":    forwarderMetrics.TotalRetries,
			"average_latency":  fmt.Sprintf("%dms", forwarderMetrics.AverageLatency),
			"success_rate":     fmt.Sprintf("%.2f%%", h.forwarder.GetSuccessRate()),
		},
	}, "Traffic mirror statistics")
}

// Close 关闭消息处理器
func (h *MessageHandler) Close() error {
	h.logManager.LogSystem(logrus.InfoLevel, nil, "Closing message handler")

	// 打印最终统计信息
	h.PrintStats()

	// 关闭转发器
	if err := h.forwarder.Close(); err != nil {
		h.logManager.LogError(logrus.ErrorLevel, logrus.Fields{"error": err.Error()}, "Failed to close forwarder")
		return err
	}

	h.logManager.LogSystem(logrus.InfoLevel, nil, "Message handler closed")
	return nil
}
