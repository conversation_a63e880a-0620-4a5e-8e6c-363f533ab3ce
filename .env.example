# 流量镜像工具环境变量配置示例 - 优化消费速率版本
# 复制此文件为 .env 并根据实际环境修改配置

# ===========================================
# Kafka 配置
# ===========================================

# Kafka集群地址 (逗号分隔多个地址)
KAFKA_BROKERS=************:19092

# 消费的主题名称
KAFKA_TOPIC=lynxiao_flow

# 消费者组ID
KAFKA_GROUP_ID=traffic-mirror-consumer

# 并行工作数 (提高消费速率，建议设置为分区数的2-4倍)
KAFKA_PARALLEL_WORKERS=20

# ===========================================
# 目标API配置
# ===========================================

# 目标API地址
TARGET_API_URL=http://localhost:50409/v1/search

# 请求超时时间 (秒，缩短以提高处理速度)
TARGET_API_TIMEOUT=5

# ===========================================
# 消息过滤配置
# ===========================================

# 需要处理的消息类型
MESSAGE_TYPE=SearchAPI-Request

# ===========================================
# 日志配置
# ===========================================

# 日志级别: debug, info, warn, error
LOG_LEVEL=info

# ===========================================
# 性能优化建议
# ===========================================

# 高吞吐量环境建议配置:
# KAFKA_PARALLEL_WORKERS=50
# TARGET_API_TIMEOUT=3
# LOG_LEVEL=warn

# 低延迟环境建议配置:
# KAFKA_PARALLEL_WORKERS=10
# TARGET_API_TIMEOUT=1
# LOG_LEVEL=error

# ===========================================
# 部署环境示例
# ===========================================

# 开发环境
# KAFKA_BROKERS=localhost:9092
# KAFKA_PARALLEL_WORKERS=5
# TARGET_API_URL=http://localhost:8080/api/search
# LOG_LEVEL=debug

# 测试环境
# KAFKA_BROKERS=test-kafka:9092
# KAFKA_PARALLEL_WORKERS=10
# TARGET_API_URL=http://test-api:8080/v1/search
# LOG_LEVEL=info

# 生产环境
# KAFKA_BROKERS=prod-kafka-1:9092,prod-kafka-2:9092,prod-kafka-3:9092
# KAFKA_PARALLEL_WORKERS=30
# TARGET_API_URL=https://api.production.com/v1/search
# LOG_LEVEL=warn
