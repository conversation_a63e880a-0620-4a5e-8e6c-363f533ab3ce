package message

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/sirupsen/logrus"
)

// KafkaMessage Kafka消息结构（基于demo.json）
type KafkaMessage struct {
	Index   string     `json:"_index"`
	Type    string     `json:"_type"`
	ID      string     `json:"_id"`
	Version int        `json:"_version"`
	Score   float64    `json:"_score"`
	Source  SourceData `json:"_source"`
	Fields  FieldsData `json:"fields,omitempty"`
}

// SourceData 消息源数据
type SourceData struct {
	Level       string            `json:"level"`
	Action      string            `json:"action"`
	Type        string            `json:"type"`
	Port        int               `json:"port"`
	SpanID      string            `json:"spanId"`
	Topic       string            `json:"topic"`
	IP          string            `json:"ip"`
	SubjectCode string            `json:"subjectCode"`
	Data        string            `json:"data"` // JSON字符串
	AppID       string            `json:"appId"`
	Group       string            `json:"group"`
	Labels      map[string]string `json:"labels"`
	FromID      string            `json:"fromId"`
	PID         int               `json:"pid"`
	Time        int64             `json:"time"`
	Version     string            `json:"@version"`
	UUID        string            `json:"uuid"`
	TraceID     string            `json:"traceId"`
	Timestamp   string            `json:"@timestamp"`
}

// FieldsData 字段数据
type FieldsData struct {
	Data []string `json:"data,omitempty"`
	Type []string `json:"type,omitempty"`
}

// SearchAPIRequest 搜索API请求结构
type SearchAPIRequest struct {
	Header    RequestHeader    `json:"header"`
	Parameter RequestParameter `json:"parameter"`
	Payload   RequestPayload   `json:"payload"`
}

// RequestHeader 请求头
type RequestHeader struct {
	AppID    string `json:"appId"`
	ProdCode string `json:"prodCode"`
	Token    string `json:"token,omitempty"`
	PVID     string `json:"pvid,omitempty"`
	Region   string `json:"region,omitempty"`
	TopK     int    `json:"topK,omitempty"`
	TraceID  string `json:"traceId"`
}

// RequestParameter 请求参数
type RequestParameter struct {
	ID                      string `json:"id,omitempty"`
	CurrentWorkflowNodeCode string `json:"current.workflow.nodeCode,omitempty"`
}

// RequestPayload 请求载荷
type RequestPayload struct {
	AppID    string   `json:"appId"`
	FromType string   `json:"fromType,omitempty"`
	Intent   string   `json:"intent"`
	Query    []string `json:"query"`
	TopK     int      `json:"topK,omitempty"`
	Scene    *string  `json:"scene"`
}

// Parser 消息解析器
type Parser struct {
	systemLogger  *logrus.Logger
	requestLogger *logrus.Logger
	messageType   string
}

// NewParser 创建新的消息解析器
func NewParser(systemLogger *logrus.Logger, messageType string) *Parser {
	return &Parser{
		systemLogger:  systemLogger,
		requestLogger: systemLogger, // 暂时使用同一个logger，稍后会修改
		messageType:   messageType,
	}
}

// ParseMessage 解析Kafka消息
func (p *Parser) ParseMessage(data []byte) (*KafkaMessage, error) {
	var message KafkaMessage
	if err := json.Unmarshal(data, &message); err != nil {
		return nil, fmt.Errorf("failed to parse kafka message: %w", err)
	}
	return &message, nil
}

// ShouldProcess 判断消息是否需要处理
func (p *Parser) ShouldProcess(message *KafkaMessage) bool {
	// fmt.Println(message)

	// 检查消息类型
	if message.Source.Type != p.messageType {
		p.requestLogger.WithFields(logrus.Fields{
			"expected_type": p.messageType,
			"actual_type":   message.Source.Type,
			"trace_id":      message.Source.TraceID,
		}).Debug("Message type mismatch, skipping")
		return false
	}

	// 检查data字段是否存在
	if strings.TrimSpace(message.Source.Data) == "" {
		p.requestLogger.WithFields(logrus.Fields{
			"type":     message.Source.Type,
			"trace_id": message.Source.TraceID,
		}).Warn("Message data field is empty, skipping")
		return false
	}

	return true
}

// ExtractSearchAPIRequest 提取SearchAPI请求数据
func (p *Parser) ExtractSearchAPIRequest(message *KafkaMessage) (*SearchAPIRequest, error) {
	// 解析data字段中的JSON
	var request SearchAPIRequest
	if err := json.Unmarshal([]byte(message.Source.Data), &request); err != nil {
		return nil, fmt.Errorf("failed to parse search api request from data field: %w", err)
	}

	// 验证必要字段
	if err := p.validateSearchAPIRequest(&request); err != nil {
		return nil, fmt.Errorf("invalid search api request: %w", err)
	}

	p.requestLogger.WithFields(logrus.Fields{
		"trace_id": request.Header.TraceID,
		"app_id":   request.Header.AppID,
		"query":    request.Payload.Query,
	}).Debug("Successfully extracted search API request")

	return &request, nil
}

// validateSearchAPIRequest 验证SearchAPI请求的必要字段
func (p *Parser) validateSearchAPIRequest(request *SearchAPIRequest) error {
	// 验证header
	if request.Header.AppID == "" {
		return fmt.Errorf("header.appId is required")
	}
	if request.Header.TraceID == "" {
		return fmt.Errorf("header.traceId is required")
	}

	// 验证payload
	if request.Payload.AppID == "" {
		return fmt.Errorf("payload.appId is required")
	}
	if len(request.Payload.Query) == 0 {
		return fmt.Errorf("payload.query is required and cannot be empty")
	}
	if request.Payload.Intent == "" {
		return fmt.Errorf("payload.intent is required")
	}

	return nil
}

// ProcessMessage 处理消息的完整流程 - 检查data字段中的搜索请求
func (p *Parser) ProcessMessage(data []byte) (*SearchAPIRequest, error) {
	// 首先尝试直接解析为通用JSON
	var rawMessage map[string]interface{}
	if err := json.Unmarshal(data, &rawMessage); err != nil {
		p.requestLogger.WithError(err).Debug("Failed to parse message as JSON")
		return nil, nil // 跳过非JSON消息
	}

	// 获取data字段
	var dataStr string
	if dataField, exists := rawMessage["data"]; exists {
		if dataString, ok := dataField.(string); ok {
			dataStr = dataString
		}
	}

	// 如果没有data字段，跳过
	if strings.TrimSpace(dataStr) == "" {
		p.requestLogger.Debug("No data field found, skipping message")
		return nil, nil
	}

	// 尝试解析data字段为SearchAPI请求
	var searchRequest SearchAPIRequest
	if err := json.Unmarshal([]byte(dataStr), &searchRequest); err != nil {
		p.requestLogger.WithError(err).Debug("Data field is not a valid SearchAPI request, skipping")
		return nil, nil
	}

	// 验证这是一个有效的搜索请求（检查必要字段）
	if err := p.validateSearchAPIRequest(&searchRequest); err != nil {
		p.requestLogger.WithError(err).Debug("Data field does not contain valid SearchAPI request, skipping")
		return nil, nil
	}

	// 记录找到的搜索请求
	messageType := ""
	if msgType, exists := rawMessage["type"]; exists {
		if typeStr, ok := msgType.(string); ok {
			messageType = typeStr
		}
	}

	p.requestLogger.WithFields(logrus.Fields{
		"message_type": messageType,
		"trace_id":     searchRequest.Header.TraceID,
		"app_id":       searchRequest.Header.AppID,
		"query":        searchRequest.Payload.Query,
	}).Info("Found valid SearchAPI request in message data")

	return &searchRequest, nil
}

// GetMessageInfo 获取消息基本信息（用于日志记录）
func (p *Parser) GetMessageInfo(data []byte) map[string]interface{} {
	// 首先尝试解析为通用JSON
	var rawMessage map[string]interface{}
	if err := json.Unmarshal(data, &rawMessage); err != nil {
		return map[string]interface{}{
			"error": err.Error(),
		}
	}

	// 打印原始消息用于调试
	p.requestLogger.WithField("raw_message", rawMessage).Debug("Raw Kafka message")

	// 尝试提取基本信息
	info := map[string]interface{}{}

	// 检查直接的type字段
	if msgType, ok := rawMessage["type"]; ok {
		info["type"] = msgType
	}

	// 检查_source中的信息
	if source, ok := rawMessage["_source"].(map[string]interface{}); ok {
		if sourceType, ok := source["type"]; ok {
			info["type"] = sourceType
		}
		if traceID, ok := source["traceId"]; ok {
			info["trace_id"] = traceID
		}
		if appID, ok := source["appId"]; ok {
			info["app_id"] = appID
		}
		if action, ok := source["action"]; ok {
			info["action"] = action
		}
		if uuid, ok := source["uuid"]; ok {
			info["uuid"] = uuid
		}
	}

	return info
}
