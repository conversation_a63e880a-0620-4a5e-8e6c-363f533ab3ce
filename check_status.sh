#!/bin/bash

# 流量镜像工具状态检查脚本

echo "=== 流量镜像工具状态检查 ==="

# 检查进程是否运行
echo "1. 检查进程状态..."
if pgrep -f "traffic-mirror" > /dev/null; then
    echo "✅ traffic-mirror 进程正在运行"
    echo "   PID: $(pgrep -f traffic-mirror)"
    echo "   运行时间: $(ps -o etime= -p $(pgrep -f traffic-mirror))"
else
    echo "❌ traffic-mirror 进程未运行"
    exit 1
fi

# 检查网络连接
echo ""
echo "2. 检查Kafka连接..."
if timeout 5 bash -c 'cat < /dev/null > /dev/tcp/10.101.1.105/19092' 2>/dev/null; then
    echo "✅ Kafka连接正常 (10.101.1.105:19092)"
else
    echo "❌ 无法连接到Kafka (10.101.1.105:19092)"
fi

# 检查目标API连接
echo ""
echo "3. 检查目标API连接..."
if timeout 5 bash -c 'cat < /dev/null > /dev/tcp/0.0.0.0/50409' 2>/dev/null; then
    echo "✅ 目标API连接正常 (0.0.0.0:50409)"
else
    echo "⚠️  目标API连接失败 (0.0.0.0:50409) - 这是正常的，如果目标服务未启动"
fi

# 检查日志文件
echo ""
echo "4. 检查最近的日志..."
if [ -f "./logs/traffic-mirror.log" ]; then
    echo "最近5条日志:"
    tail -5 ./logs/traffic-mirror.log
else
    echo "日志文件不存在，应用可能输出到stdout"
fi

# 检查配置文件
echo ""
echo "5. 检查配置文件..."
if [ -f "config_prod.yaml" ]; then
    echo "✅ 配置文件存在: config_prod.yaml"
    echo "Kafka Topic: $(grep 'topic:' config_prod.yaml | awk '{print $2}' | tr -d '"')"
    echo "Consumer Group: $(grep 'group_id:' config_prod.yaml | awk '{print $2}' | tr -d '"')"
    echo "Target API: $(grep 'url:' config_prod.yaml | awk '{print $2}' | tr -d '"')"
else
    echo "❌ 配置文件不存在"
fi

# 显示系统资源使用情况
echo ""
echo "6. 系统资源使用情况..."
if pgrep -f "traffic-mirror" > /dev/null; then
    PID=$(pgrep -f traffic-mirror)
    echo "CPU使用率: $(ps -o %cpu= -p $PID)%"
    echo "内存使用: $(ps -o %mem= -p $PID)%"
    echo "虚拟内存: $(ps -o vsz= -p $PID) KB"
    echo "物理内存: $(ps -o rss= -p $PID) KB"
fi

echo ""
echo "=== 状态检查完成 ==="
