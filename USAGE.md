# 流量镜像工具使用指南

[![Version](https://img.shields.io/badge/Version-v1.0.0-blue.svg)](https://github.com/your-org/traffic-mirror/releases)
[![Documentation](https://img.shields.io/badge/Documentation-Complete-green.svg)](USAGE.md)
[![Support](https://img.shields.io/badge/Support-Active-brightgreen.svg)](https://github.com/your-org/traffic-mirror/issues)

一个高性能、生产级的Golang流量镜像工具，专为企业级部署设计。从Kafka实时消费消息并转发到目标API，支持完整的日志管理、监控统计和故障恢复机制。

## 📋 目录

- [快速开始](#快速开始)
- [环境要求](#环境要求)
- [配置详解](#配置详解)
- [部署方式](#部署方式)
- [监控和日志](#监控和日志)
- [故障排查](#故障排查)
- [性能调优](#性能调优)
- [最佳实践](#最佳实践)
- [API参考](#api参考)
- [运维手册](#运维手册)
- [安全指南](#安全指南)
- [FAQ常见问题](#faq常见问题)

## 🚀 快速开始

### 环境要求

**系统要求**：
- Linux系统（推荐CentOS 7+/Ubuntu 18.04+）
- Go 1.19+ （仅编译时需要）
- 可访问的Kafka集群
- 目标API服务

**资源要求**：
- CPU: 2核心（推荐4核心）
- 内存: 1GB（推荐2GB）
- 磁盘: 10GB（用于日志存储，推荐50GB）
- 网络: 稳定的内网连接

### 一键部署

**方式一：自动部署脚本（推荐）**

```bash
# 下载项目
git clone <repository-url>
cd traffic-mirror

# 运行自动部署脚本（需要root权限）
sudo ./deploy.sh
```

部署脚本会自动完成：
- ✅ 创建专用用户和目录
- ✅ 编译应用程序
- ✅ 安装systemd服务
- ✅ 配置系统参数
- ✅ 启动服务并验证

**方式二：手动部署**

```bash
# 1. 编译应用
go mod tidy
go build -o traffic-mirror .

# 2. 配置文件（选择环境）
cp config_prod.yaml config.yaml  # 生产环境
# 或
cp config_dev.yaml config.yaml   # 开发环境

# 3. 启动应用
./traffic-mirror -config config.yaml
```

### 验证部署

```bash
# 检查服务状态
sudo systemctl status traffic-mirror

# 查看实时日志
sudo journalctl -u traffic-mirror -f

# 运行状态检查脚本
./check_status.sh
```

## 📝 配置详解

项目提供了两套预配置的环境配置文件：

### 配置文件选择

| 配置文件 | 用途 | 特点 | 适用场景 |
|---------|------|------|---------|
| `config_prod.yaml` | 生产环境 | 分层日志、文件输出、性能优化 | 生产部署、长期运行 |
| `config_dev.yaml` | 开发环境 | 控制台输出、详细调试信息 | 本地开发、问题调试 |

### 核心配置项

#### 1. Kafka配置

```yaml
kafka:
  brokers:
    - "************:19092"      # Kafka集群地址，支持多个broker
    - "10.101.1.106:19092"      # 高可用配置
  topic: "lynxiao_flow"         # 消费的topic名称
  group_id: "traffic-mirror-consumer"  # 消费者组ID，确保唯一性
  consumer:
    offset_initial: "newest"    # newest: 只消费最新消息, earliest: 从头开始
    session_timeout: 30         # 会话超时时间（秒）
    heartbeat_interval: 3       # 心跳间隔（秒）
```

**配置说明**：
- `brokers`: Kafka集群地址列表，建议配置多个以提高可用性
- `topic`: 要消费的Kafka主题名称
- `group_id`: 消费者组ID，同一组内的消费者会分摊消息处理
- `offset_initial`: 初始消费位置，`newest`适合实时处理，`earliest`适合历史数据处理

#### 2. 目标API配置

```yaml
target_api:
  url: "http://*************:50409/v1/search"  # 目标API完整地址
  timeout: 30                                   # 请求超时时间（秒）
  retry_count: 3                               # 失败重试次数
  retry_interval: 1                            # 重试间隔（秒）
```

**配置说明**：
- `url`: 目标API的完整URL地址
- `timeout`: HTTP请求超时时间，建议根据API响应时间调整
- `retry_count`: 请求失败时的重试次数，避免设置过高
- `retry_interval`: 重试间隔时间，采用指数退避策略

#### 3. 消息过滤配置

```yaml
filter:
  message_type: "SearchAPI-Request"  # 要处理的消息类型
```

**配置说明**：
- `message_type`: 只处理指定类型的消息，其他消息会被跳过

#### 4. 分层日志配置

```yaml
log:
  level: "error"        # 全局日志级别: debug, info, warn, error
  format: "json"        # 日志格式: json, text

  # 请求处理日志 - 记录详细的请求处理过程
  request:
    enabled: true
    output: "file"                    # 输出到文件
    file_path: "./logs/request.log"
    level: "info"

  # 统计监控日志 - 实时性能指标
  stats:
    enabled: true
    output: "stdout"                  # 输出到控制台
    level: "info"

  # 错误日志 - 系统错误和异常
  error:
    enabled: true
    output: "both"                    # 同时输出到控制台和文件
    file_path: "./logs/error.log"
    level: "error"

  # 系统日志 - 应用启动、关闭等系统事件
  system:
    enabled: true
    output: "stdout"                  # 输出到控制台
    level: "info"
```

**日志分层说明**：
- **请求日志**: 记录每个消息的处理详情，用于审计和调试
- **统计日志**: 实时输出性能指标，用于监控
- **错误日志**: 记录所有错误和异常，用于故障排查
- **系统日志**: 记录应用生命周期事件

#### 5. 应用配置

```yaml
app:
  name: "traffic-mirror"
  version: "1.0.0"
  enable_metrics: true      # 启用性能监控统计
  metrics_interval: 60      # 统计输出间隔（秒）
```

## 🚀 部署方式

### 开发环境部署

**适用场景**: 本地开发、功能测试、问题调试

```bash
# 1. 克隆项目
git clone <repository-url>
cd traffic-mirror

# 2. 安装依赖
go mod tidy

# 3. 使用开发配置运行
go run main.go -config config_dev.yaml

# 或编译后运行
go build -o traffic-mirror .
./traffic-mirror -config config_dev.yaml
```

**开发环境特点**:
- 所有日志输出到控制台，便于实时查看
- 启用debug级别日志，提供详细调试信息
- 快速启动，适合频繁重启测试

### 生产环境部署

**适用场景**: 正式环境、长期运行、高可用部署

#### 方式一：自动部署（推荐）

```bash
# 使用自动部署脚本
sudo ./deploy.sh
```

自动部署包含：
- ✅ 系统要求检查
- ✅ 创建专用用户 `traffic-mirror`
- ✅ 创建目录结构 `/opt/traffic-mirror`
- ✅ 编译和安装应用
- ✅ 配置systemd服务
- ✅ 优化系统参数
- ✅ 启动服务并验证

#### 方式二：手动部署

```bash
# 1. 创建用户和目录
sudo useradd -r -s /bin/false traffic-mirror
sudo mkdir -p /opt/traffic-mirror/logs
sudo chown -R traffic-mirror:traffic-mirror /opt/traffic-mirror

# 2. 编译和安装
go build -o traffic-mirror .
sudo cp traffic-mirror /opt/traffic-mirror/
sudo cp config_prod.yaml /opt/traffic-mirror/
sudo chmod +x /opt/traffic-mirror/traffic-mirror

# 3. 创建systemd服务
sudo cp traffic-mirror.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable traffic-mirror
sudo systemctl start traffic-mirror
```

### 容器化部署

**适用场景**: 容器环境、微服务架构、云原生部署

```dockerfile
# Dockerfile示例
FROM golang:1.19-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod tidy && go build -o traffic-mirror .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/traffic-mirror .
COPY --from=builder /app/config_prod.yaml .
CMD ["./traffic-mirror", "-config", "config_prod.yaml"]
```

```bash
# 构建和运行
docker build -t traffic-mirror .
docker run -d --name traffic-mirror \
  -v /opt/traffic-mirror/logs:/root/logs \
  traffic-mirror
```

## 📊 监控和日志

### 实时监控指标

应用每60秒输出一次统计信息到控制台：

```json
{
  "handler_metrics": {
    "total_messages": 47302,        # 总消息数
    "processed_messages": 384,      # 已处理消息数
    "skipped_messages": 46917,      # 跳过的消息数
    "error_messages": 0,            # 错误消息数
    "processing_rate": "394.18 msg/s",  # 处理速率
    "success_rate": "0.81%"         # 成功率
  },
  "forwarder_metrics": {
    "total_requests": 385,          # 总请求数
    "success_requests": 384,        # 成功请求数
    "failed_requests": 0,           # 失败请求数
    "total_retries": 0,             # 总重试次数
    "average_latency": "185ms",     # 平均延迟
    "success_rate": "99.74%"        # 成功率
  }
}
```

### 日志文件管理

**日志轮转配置**:
- 单个文件最大500MB
- 保留7个备份文件
- 自动压缩旧文件
- 按时间戳命名

**日志文件位置**:
```bash
/opt/traffic-mirror/logs/
├── request.log          # 请求处理日志
├── error.log           # 错误日志
├── request.log.1.gz    # 轮转备份文件
└── error.log.1.gz      # 轮转备份文件
```

### 日志查看命令

```bash
# 查看实时统计日志（控制台输出）
sudo journalctl -u traffic-mirror -f

# 查看请求处理日志
tail -f /opt/traffic-mirror/logs/request.log

# 查看错误日志
tail -f /opt/traffic-mirror/logs/error.log

# 查看最近的错误
grep "error" /opt/traffic-mirror/logs/error.log | tail -10

# 分析处理速率
grep "processing_rate" /opt/traffic-mirror/logs/request.log | tail -5
```

## 🔧 故障排查

### 常见问题诊断

#### 1. Kafka连接问题

**症状**: 应用启动后无法连接Kafka

```bash
# 错误日志示例
{"level":"error","message":"Error reading message","error":"dial tcp: connection refused"}
```

**排查步骤**:

```bash
# 1. 检查网络连通性
telnet ************ 19092

# 2. 检查Kafka集群状态
kafka-topics.sh --list --bootstrap-server ************:19092

# 3. 验证topic存在
kafka-topics.sh --describe --topic lynxiao_flow --bootstrap-server ************:19092

# 4. 检查消费者组
kafka-consumer-groups.sh --bootstrap-server ************:19092 --list
```

**解决方案**:
- 确认Kafka集群地址正确
- 检查网络策略和防火墙设置
- 验证topic存在且有权限访问
- 确认消费者组ID唯一性

#### 2. HTTP转发失败

**症状**: 消息处理成功但API转发失败

```bash
# 错误日志示例
{"level":"error","message":"Failed to forward request","error":"context deadline exceeded"}
```

**排查步骤**:

```bash
# 1. 检查目标API可用性
curl -X POST http://*************:50409/v1/search \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'

# 2. 检查网络延迟
ping *************

# 3. 检查DNS解析（如果使用域名）
nslookup your-api-domain.com

# 4. 验证API响应时间
time curl -X POST http://*************:50409/v1/search
```

**解决方案**:
- 确认目标API服务正常运行
- 调整timeout配置适应API响应时间
- 检查网络策略和防火墙
- 验证API接口格式和参数

#### 3. 消息解析错误

**症状**: 大量消息被跳过，处理率很低

```bash
# 日志示例
{"level":"debug","message":"Message type mismatch, skipping","expected_type":"SearchAPI-Request","actual_type":"Other"}
```

**排查步骤**:

```bash
# 1. 查看消息格式示例
kafka-console-consumer.sh --bootstrap-server ************:19092 \
  --topic lynxiao_flow --max-messages 1

# 2. 启用debug日志查看详细解析过程
# 修改配置文件 log.level: "debug"

# 3. 检查消息类型统计
grep "Message type" /opt/traffic-mirror/logs/request.log | \
  awk '{print $NF}' | sort | uniq -c
```

**解决方案**:
- 确认filter.message_type配置正确
- 验证Kafka消息格式符合预期
- 检查消息中的type字段值
- 确认data字段包含有效JSON

### 性能问题排查

#### 处理速率低

**排查步骤**:

```bash
# 1. 查看系统资源使用
top -p $(pgrep traffic-mirror)

# 2. 检查网络连接数
netstat -an | grep :19092 | wc -l
netstat -an | grep :50409 | wc -l

# 3. 分析处理瓶颈
grep "average_latency" /opt/traffic-mirror/logs/request.log | tail -10
```

**优化建议**:
- 调整Kafka消费者配置
- 优化HTTP请求超时时间
- 增加系统资源配置
- 检查目标API性能

### 调试模式

**启用详细日志**:

```bash
# 方式一：使用开发配置
./traffic-mirror -config config_dev.yaml

# 方式二：临时修改生产配置
# 在config_prod.yaml中设置:
log:
  level: "debug"
  request:
    level: "debug"
```

**调试输出示例**:

```json
{"level":"debug","message":"Received message","topic":"lynxiao_flow","partition":2,"offset":316412558}
{"level":"debug","message":"Message type match","type":"SearchAPI-Request"}
{"level":"debug","message":"Parsing data field","data_length":245}
{"level":"debug","message":"Successfully parsed SearchAPI request","app_id":"cc501f15","query":["测试查询"]}
```

### 健康检查脚本

项目提供了自动化健康检查脚本：

```bash
# 运行健康检查
./check_status.sh
```

**检查内容**:
- ✅ 进程运行状态
- ✅ Kafka连接状态
- ✅ 目标API连接状态
- ✅ 日志文件状态
- ✅ 配置文件验证
- ✅ 系统资源使用情况

## ⚡ 性能调优

### 系统级优化

#### 文件描述符限制

```bash
# 检查当前限制
ulimit -n

# 永久设置（需要重启）
echo "traffic-mirror soft nofile 65536" >> /etc/security/limits.conf
echo "traffic-mirror hard nofile 65536" >> /etc/security/limits.conf

# 临时设置（当前会话）
ulimit -n 65536
```

#### 网络参数优化

```bash
# 优化TCP连接参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" >> /etc/sysctl.conf
echo "net.core.rmem_max = 16777216" >> /etc/sysctl.conf
echo "net.core.wmem_max = 16777216" >> /etc/sysctl.conf

# 应用配置
sysctl -p
```

#### 内存和CPU优化

```bash
# 检查系统资源
free -h
cat /proc/cpuinfo | grep processor | wc -l

# 设置进程优先级（可选）
renice -10 $(pgrep traffic-mirror)
```

### 应用级优化

#### 高性能配置示例

```yaml
# 针对高吞吐量场景的优化配置
kafka:
  consumer:
    session_timeout: 10          # 减少会话超时时间
    heartbeat_interval: 3        # 保持心跳频率

target_api:
  timeout: 10                    # 根据API响应时间调整
  retry_count: 2                 # 减少重试次数，提高吞吐量
  retry_interval: 1              # 快速重试

app:
  metrics_interval: 30           # 更频繁的监控输出

log:
  level: "error"                 # 减少日志输出，提高性能
  request:
    level: "warn"                # 只记录警告和错误
```

#### 低延迟配置示例

```yaml
# 针对低延迟场景的优化配置
kafka:
  consumer:
    session_timeout: 30          # 保持稳定连接
    heartbeat_interval: 3

target_api:
  timeout: 5                     # 快速超时
  retry_count: 1                 # 最少重试
  retry_interval: 0.5            # 快速重试

app:
  metrics_interval: 60           # 减少监控开销
```

### 性能基准测试

#### 测试环境搭建

```bash
# 1. 启动模拟API服务器
cd test
go run mock_server.go &

# 2. 使用测试配置
cp test_config.yaml config.yaml

# 3. 运行性能测试
./traffic-mirror -config config.yaml
```

#### 性能指标参考

**正常性能指标**:
- 处理速率: 300-500 msg/s
- 转发成功率: >99%
- 平均延迟: <200ms
- 内存使用: <100MB
- CPU使用: <50%

**高性能指标**:
- 处理速率: 500-1000 msg/s
- 转发成功率: >99.5%
- 平均延迟: <100ms
- 内存使用: <200MB
- CPU使用: <80%

## 📋 最佳实践

### 部署最佳实践

#### 1. 环境隔离

```bash
# 不同环境使用不同的消费者组ID
# 开发环境
group_id: "traffic-mirror-consumer-dev"

# 测试环境
group_id: "traffic-mirror-consumer-test"

# 生产环境
group_id: "traffic-mirror-consumer-prod"
```

#### 2. 配置管理

```bash
# 使用版本控制管理配置文件
git add config_prod.yaml
git commit -m "Update production config"

# 配置文件备份
cp config_prod.yaml config_prod.yaml.$(date +%Y%m%d)
```

#### 3. 服务监控

```bash
# 设置监控告警
# 1. 进程存活监控
# 2. 日志错误率监控
# 3. 处理速率监控
# 4. 系统资源监控
```

### 运维最佳实践

#### 1. 日志管理

```bash
# 配置日志轮转
sudo tee /etc/logrotate.d/traffic-mirror << EOF
/opt/traffic-mirror/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 traffic-mirror traffic-mirror
    postrotate
        systemctl reload traffic-mirror
    endscript
}
EOF
```

#### 2. 备份策略

```bash
# 配置文件备份
sudo cp /opt/traffic-mirror/config_prod.yaml \
       /opt/traffic-mirror/backup/config_prod.yaml.$(date +%Y%m%d)

# 应用程序备份
sudo cp /opt/traffic-mirror/traffic-mirror \
       /opt/traffic-mirror/backup/traffic-mirror.$(date +%Y%m%d)
```

#### 3. 更新策略

```bash
# 滚动更新步骤
# 1. 备份当前版本
sudo systemctl stop traffic-mirror
sudo cp /opt/traffic-mirror/traffic-mirror /opt/traffic-mirror/backup/

# 2. 部署新版本
sudo cp new-traffic-mirror /opt/traffic-mirror/traffic-mirror

# 3. 验证配置
./traffic-mirror -config config_prod.yaml -validate

# 4. 启动服务
sudo systemctl start traffic-mirror

# 5. 验证运行状态
./check_status.sh
```

### 安全最佳实践

#### 1. 网络安全

```bash
# 防火墙配置示例
# 只允许必要的端口访问
sudo ufw allow from **********/24 to any port 19092  # Kafka
sudo ufw allow from ************/24 to any port 50409  # Target API

# 禁用不必要的服务
sudo systemctl disable unnecessary-service
```

#### 2. 用户权限

```bash
# 使用最小权限原则
sudo chown -R traffic-mirror:traffic-mirror /opt/traffic-mirror
sudo chmod 750 /opt/traffic-mirror
sudo chmod 640 /opt/traffic-mirror/config_prod.yaml
sudo chmod 755 /opt/traffic-mirror/traffic-mirror
```

#### 3. 日志安全

```bash
# 避免记录敏感信息
# 在配置中设置适当的日志级别
log:
  level: "info"  # 避免debug级别在生产环境

# 定期清理敏感日志
find /opt/traffic-mirror/logs -name "*.log" -mtime +30 -delete
```

## 📚 API参考

### 消息格式规范

#### Kafka消息格式

工具处理的Kafka消息必须符合以下JSON格式：

```json
{
  "_source": {
    "type": "SearchAPI-Request",
    "data": "{\"header\":{\"appId\":\"cc501f15\",\"prodCode\":\"HealthySearch\",\"traceId\":\"xxx\"},\"parameter\":{\"id\":\"xxx\"},\"payload\":{\"appId\":\"cc501f15\",\"intent\":\"2\",\"query\":[\"查询内容\"]}}"
  }
}
```

**字段说明**:
- `_source.type`: 消息类型，必须为 `SearchAPI-Request`
- `_source.data`: JSON字符串，包含实际的API请求数据

#### 目标API格式

解析后的数据会以POST请求发送到目标API：

```json
{
  "header": {
    "appId": "cc501f15",
    "prodCode": "HealthySearch",
    "traceId": "xxx"
  },
  "parameter": {
    "id": "xxx"
  },
  "payload": {
    "appId": "cc501f15",
    "intent": "2",
    "query": ["查询内容"]
  }
}
```

### 命令行参数

```bash
./traffic-mirror [选项]

选项:
  -config string
        配置文件路径 (默认: "config.yaml")
  -version
        显示版本信息
  -help
        显示帮助信息
  -validate
        验证配置文件格式（不启动服务）
```

### 环境变量

```bash
# 可通过环境变量覆盖配置
export KAFKA_BROKERS="************:19092,10.101.1.106:19092"
export KAFKA_TOPIC="lynxiao_flow"
export TARGET_API_URL="http://*************:50409/v1/search"
export LOG_LEVEL="info"
```

### 退出码

| 退出码 | 含义 | 说明 |
|-------|------|------|
| 0 | 正常退出 | 应用正常关闭 |
| 1 | 配置错误 | 配置文件格式错误或缺失 |
| 2 | 连接错误 | 无法连接Kafka或目标API |
| 3 | 权限错误 | 文件权限或网络权限不足 |
| 130 | 用户中断 | 用户按Ctrl+C中断 |

### 信号处理

```bash
# 优雅关闭（推荐）
kill -TERM $(pgrep traffic-mirror)

# 立即关闭
kill -KILL $(pgrep traffic-mirror)

# 重新加载配置（如果支持）
kill -HUP $(pgrep traffic-mirror)
```

## 🔒 安全建议

### 生产环境安全清单

- [ ] 使用专用用户运行服务
- [ ] 配置防火墙规则
- [ ] 设置文件权限
- [ ] 启用日志轮转
- [ ] 定期备份配置
- [ ] 监控异常访问
- [ ] 更新系统补丁
- [ ] 配置资源限制

### 网络安全

```bash
# 1. 防火墙配置
sudo ufw enable
sudo ufw default deny incoming
sudo ufw allow ssh
sudo ufw allow from trusted-network to any port kafka-port

# 2. 网络隔离
# 将应用部署在内网环境
# 使用VPN或专线连接

# 3. TLS/SSL配置（如果Kafka支持）
kafka:
  security_protocol: "SSL"
  ssl_ca_location: "/path/to/ca-cert"
  ssl_certificate_location: "/path/to/client-cert"
  ssl_key_location: "/path/to/client-key"
```

### 数据安全

```bash
# 1. 敏感数据处理
# 避免在日志中记录敏感信息
# 对配置文件进行加密存储

# 2. 访问控制
# 限制配置文件访问权限
sudo chmod 600 /opt/traffic-mirror/config_prod.yaml

# 3. 审计日志
# 记录所有配置变更和访问行为
```

## 📞 支持和维护

### 技术支持

- 📖 查看项目文档和README
- 🐛 提交GitHub Issue报告问题
- 💬 联系维护团队获取支持
- 📧 发送邮件至技术支持邮箱

### 版本更新

```bash
# 检查当前版本
./traffic-mirror -version

# 下载新版本
wget https://releases/traffic-mirror-v1.1.0

# 执行更新
sudo systemctl stop traffic-mirror
sudo cp traffic-mirror-v1.1.0 /opt/traffic-mirror/traffic-mirror
sudo systemctl start traffic-mirror
```

### 社区贡献

欢迎通过以下方式贡献：
- 🔧 提交代码改进
- 📝 完善文档
- 🧪 提供测试用例
- 💡 提出功能建议

## 📋 运维手册

### 日常运维检查清单

#### 每日检查 (5分钟)

```bash
# 1. 服务状态检查
sudo systemctl status traffic-mirror

# 2. 快速健康检查
cd /opt/traffic-mirror && ./check_status.sh

# 3. 查看最新统计
sudo journalctl -u traffic-mirror -n 5 | grep "Traffic mirror statistics"

# 4. 检查错误日志
tail -10 /opt/traffic-mirror/logs/error.log
```

#### 每周检查 (15分钟)

```bash
# 1. 日志文件大小检查
du -sh /opt/traffic-mirror/logs/*

# 2. 系统资源使用
top -p $(pgrep traffic-mirror)
free -h

# 3. 网络连接检查
netstat -an | grep :19092  # Kafka连接
netstat -an | grep :50409  # API连接

# 4. 性能趋势分析
grep "processing_rate" /opt/traffic-mirror/logs/request.log | tail -20
```

#### 每月检查 (30分钟)

```bash
# 1. 日志轮转检查
ls -la /opt/traffic-mirror/logs/

# 2. 配置文件备份
sudo cp /opt/traffic-mirror/config.yaml \
       /opt/traffic-mirror/backup/config.yaml.$(date +%Y%m)

# 3. 性能基准测试
# 记录当前性能指标作为基准

# 4. 系统更新检查
sudo yum check-update  # CentOS/RHEL
sudo apt list --upgradable  # Ubuntu/Debian
```

### 运维自动化脚本

#### 监控脚本

```bash
#!/bin/bash
# monitor.sh - 监控脚本

LOG_FILE="/var/log/traffic-mirror-monitor.log"
ALERT_EMAIL="<EMAIL>"

# 检查服务状态
check_service() {
    if ! systemctl is-active --quiet traffic-mirror; then
        echo "$(date): ALERT - Service is down" >> $LOG_FILE
        echo "Traffic Mirror service is down" | mail -s "ALERT: Service Down" $ALERT_EMAIL
        return 1
    fi
    return 0
}

# 检查处理速率
check_processing_rate() {
    local rate=$(journalctl -u traffic-mirror -n 1 | grep "processing_rate" | \
                 grep -o '[0-9]\+\.[0-9]\+' | head -1)

    if [ -n "$rate" ] && (( $(echo "$rate < 100" | bc -l) )); then
        echo "$(date): WARNING - Low processing rate: $rate msg/s" >> $LOG_FILE
    fi
}

# 检查错误率
check_error_rate() {
    local error_count=$(grep -c "error" /opt/traffic-mirror/logs/error.log)
    local total_lines=$(wc -l < /opt/traffic-mirror/logs/request.log)

    if [ $total_lines -gt 0 ]; then
        local error_rate=$(echo "scale=4; $error_count / $total_lines * 100" | bc)
        if (( $(echo "$error_rate > 5" | bc -l) )); then
            echo "$(date): WARNING - High error rate: $error_rate%" >> $LOG_FILE
        fi
    fi
}

# 主检查流程
main() {
    check_service
    check_processing_rate
    check_error_rate
    echo "$(date): Monitor check completed" >> $LOG_FILE
}

main
```

#### 自动重启脚本

```bash
#!/bin/bash
# auto_restart.sh - 自动重启脚本

MAX_RETRIES=3
RETRY_COUNT=0

restart_service() {
    echo "$(date): Attempting to restart traffic-mirror service"
    sudo systemctl restart traffic-mirror
    sleep 10

    if systemctl is-active --quiet traffic-mirror; then
        echo "$(date): Service restarted successfully"
        return 0
    else
        echo "$(date): Service restart failed"
        return 1
    fi
}

# 主逻辑
while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if ! systemctl is-active --quiet traffic-mirror; then
        echo "$(date): Service is down, attempting restart ($((RETRY_COUNT + 1))/$MAX_RETRIES)"

        if restart_service; then
            break
        else
            RETRY_COUNT=$((RETRY_COUNT + 1))
            sleep 30
        fi
    else
        echo "$(date): Service is running normally"
        break
    fi
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    echo "$(date): CRITICAL - Failed to restart service after $MAX_RETRIES attempts"
    echo "Traffic Mirror service failed to restart" | mail -s "CRITICAL: Service Restart Failed" <EMAIL>
fi
```

### 备份和恢复

#### 配置备份

```bash
#!/bin/bash
# backup_config.sh - 配置备份脚本

BACKUP_DIR="/opt/traffic-mirror/backup"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份配置文件
cp /opt/traffic-mirror/config.yaml $BACKUP_DIR/config_$DATE.yaml

# 备份二进制文件
cp /opt/traffic-mirror/traffic-mirror $BACKUP_DIR/traffic-mirror_$DATE

# 备份服务文件
cp /etc/systemd/system/traffic-mirror.service $BACKUP_DIR/service_$DATE.service

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "config_*" -mtime +30 -delete
find $BACKUP_DIR -name "traffic-mirror_*" -mtime +30 -delete
find $BACKUP_DIR -name "service_*" -mtime +30 -delete

echo "Backup completed: $DATE"
```

#### 配置恢复

```bash
#!/bin/bash
# restore_config.sh - 配置恢复脚本

BACKUP_DIR="/opt/traffic-mirror/backup"

# 列出可用备份
echo "Available backups:"
ls -la $BACKUP_DIR/config_*

# 恢复指定备份
restore_backup() {
    local backup_file=$1

    if [ ! -f "$backup_file" ]; then
        echo "Backup file not found: $backup_file"
        exit 1
    fi

    # 停止服务
    sudo systemctl stop traffic-mirror

    # 备份当前配置
    cp /opt/traffic-mirror/config.yaml /opt/traffic-mirror/config.yaml.before_restore

    # 恢复配置
    cp "$backup_file" /opt/traffic-mirror/config.yaml

    # 启动服务
    sudo systemctl start traffic-mirror

    echo "Configuration restored from: $backup_file"
}

# 使用示例
# restore_backup "/opt/traffic-mirror/backup/config_20250724_090000.yaml"
```

## 🔒 安全指南

### 网络安全

#### 防火墙配置

```bash
# UFW配置示例
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许SSH
sudo ufw allow ssh

# 允许Kafka连接（仅限内网）
sudo ufw allow from 10.0.0.0/8 to any port 19092

# 允许目标API连接（仅限内网）
sudo ufw allow from 10.0.0.0/8 to any port 50409

# 查看规则
sudo ufw status numbered
```

#### iptables配置示例

```bash
# 基本防火墙规则
iptables -A INPUT -i lo -j ACCEPT
iptables -A INPUT -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT
iptables -A INPUT -p tcp --dport 22 -j ACCEPT
iptables -A INPUT -s 10.0.0.0/8 -p tcp --dport 19092 -j ACCEPT
iptables -A INPUT -j DROP

# 保存规则
iptables-save > /etc/iptables/rules.v4
```

### 访问控制

#### 用户权限管理

```bash
# 创建专用用户组
sudo groupadd traffic-mirror-ops

# 添加运维用户到组
sudo usermod -a -G traffic-mirror-ops ops-user

# 设置sudo权限
echo "%traffic-mirror-ops ALL=(ALL) NOPASSWD: /bin/systemctl start traffic-mirror, /bin/systemctl stop traffic-mirror, /bin/systemctl restart traffic-mirror, /bin/systemctl status traffic-mirror" >> /etc/sudoers.d/traffic-mirror
```

#### 文件权限设置

```bash
# 设置应用目录权限
sudo chown -R traffic-mirror:traffic-mirror /opt/traffic-mirror
sudo chmod 750 /opt/traffic-mirror
sudo chmod 640 /opt/traffic-mirror/config.yaml
sudo chmod 755 /opt/traffic-mirror/traffic-mirror

# 设置日志目录权限
sudo chmod 755 /opt/traffic-mirror/logs
sudo chmod 644 /opt/traffic-mirror/logs/*.log
```

### 数据安全

#### 敏感信息处理

```yaml
# 配置文件中避免明文密码
kafka:
  brokers: ["${KAFKA_BROKER}"]  # 使用环境变量
  username: "${KAFKA_USER}"
  password: "${KAFKA_PASS}"

target_api:
  url: "${TARGET_API_URL}"
  token: "${API_TOKEN}"
```

#### 日志安全

```bash
# 设置日志轮转和权限
sudo tee /etc/logrotate.d/traffic-mirror << EOF
/opt/traffic-mirror/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 traffic-mirror traffic-mirror
    postrotate
        systemctl reload traffic-mirror
    endscript
}
EOF
```

### 审计和监控

#### 审计日志

```bash
# 启用系统审计
sudo auditctl -w /opt/traffic-mirror/config.yaml -p wa -k traffic-mirror-config
sudo auditctl -w /opt/traffic-mirror/traffic-mirror -p x -k traffic-mirror-exec

# 查看审计日志
sudo ausearch -k traffic-mirror-config
sudo ausearch -k traffic-mirror-exec
```

#### 安全监控

```bash
#!/bin/bash
# security_monitor.sh - 安全监控脚本

# 检查异常登录
check_failed_logins() {
    local failed_count=$(grep "Failed password" /var/log/auth.log | wc -l)
    if [ $failed_count -gt 10 ]; then
        echo "WARNING: High number of failed login attempts: $failed_count"
    fi
}

# 检查文件完整性
check_file_integrity() {
    local config_hash=$(sha256sum /opt/traffic-mirror/config.yaml | awk '{print $1}')
    local expected_hash="your_expected_hash_here"

    if [ "$config_hash" != "$expected_hash" ]; then
        echo "ALERT: Configuration file has been modified"
    fi
}

# 检查进程权限
check_process_user() {
    local process_user=$(ps -o user= -p $(pgrep traffic-mirror))
    if [ "$process_user" != "traffic-mirror" ]; then
        echo "ALERT: Process running under unexpected user: $process_user"
    fi
}

check_failed_logins
check_file_integrity
check_process_user
```

## ❓ FAQ常见问题

### 部署相关

**Q: 一键部署脚本需要什么权限？**

A: 需要root权限，因为脚本会：
- 创建系统用户
- 安装systemd服务
- 修改系统配置
- 设置文件权限

**Q: 可以在非root用户下运行吗？**

A: 可以，但需要手动配置：
```bash
# 使用当前用户运行
./traffic-mirror -config config.yaml

# 或者使用systemd用户服务
systemctl --user enable traffic-mirror
systemctl --user start traffic-mirror
```

**Q: 如何在Docker中运行？**

A: 使用提供的Docker配置：
```bash
# 构建镜像
docker build -t traffic-mirror .

# 运行容器
docker run -d --name traffic-mirror \
  -v $(pwd)/config.yaml:/app/config.yaml:ro \
  -v $(pwd)/logs:/app/logs \
  traffic-mirror
```

### 配置相关

**Q: 如何配置多个Kafka broker？**

A: 在配置文件中添加多个地址：
```yaml
kafka:
  brokers:
    - "kafka1.example.com:9092"
    - "kafka2.example.com:9092"
    - "kafka3.example.com:9092"
```

**Q: 如何调整日志级别？**

A: 修改配置文件中的日志级别：
```yaml
log:
  level: "debug"  # debug, info, warn, error
  request:
    level: "info"
  error:
    level: "error"
```

**Q: 如何配置SSL连接？**

A: 目前版本不支持SSL，计划在后续版本中添加。

### 性能相关

**Q: 处理速率很低怎么办？**

A: 检查以下几个方面：
1. Kafka连接是否正常
2. 目标API响应时间
3. 消息过滤配置是否正确
4. 系统资源是否充足

**Q: 内存使用过高怎么办？**

A: 尝试以下优化：
1. 调整日志级别为error
2. 减少统计输出频率
3. 检查是否有内存泄漏
4. 增加系统内存

**Q: 如何提高处理性能？**

A: 性能优化建议：
1. 使用SSD存储
2. 增加CPU核心数
3. 优化网络连接
4. 调整Kafka消费者配置

### 故障排查

**Q: 服务启动失败怎么办？**

A: 按以下步骤排查：
```bash
# 1. 查看详细错误
sudo journalctl -u traffic-mirror -n 50

# 2. 检查配置文件
./traffic-mirror -config config.yaml -validate

# 3. 检查文件权限
ls -la /opt/traffic-mirror/

# 4. 检查端口占用
netstat -tlnp | grep :19092
```

**Q: Kafka连接失败怎么办？**

A: 检查网络和配置：
```bash
# 测试网络连通性
telnet kafka-broker 9092

# 检查Kafka集群状态
kafka-topics.sh --list --bootstrap-server kafka-broker:9092

# 验证topic存在
kafka-topics.sh --describe --topic your-topic --bootstrap-server kafka-broker:9092
```

**Q: 目标API调用失败怎么办？**

A: 验证API可用性：
```bash
# 测试API连接
curl -X POST http://target-api/v1/search \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'

# 检查网络延迟
ping target-api-host

# 查看API服务器日志
```

### 运维相关

**Q: 如何升级到新版本？**

A: 使用以下步骤：
```bash
# 1. 停止服务
sudo systemctl stop traffic-mirror

# 2. 备份当前版本
sudo cp /opt/traffic-mirror/traffic-mirror /opt/traffic-mirror/backup/

# 3. 解压新版本
tar -xzf traffic-mirror-new-version.tar.gz

# 4. 升级部署
sudo ./quick-deploy.sh --upgrade

# 5. 验证升级
./check_status.sh
```

**Q: 如何回滚到旧版本？**

A: 恢复备份文件：
```bash
# 停止服务
sudo systemctl stop traffic-mirror

# 恢复旧版本
sudo cp /opt/traffic-mirror/backup/traffic-mirror /opt/traffic-mirror/

# 启动服务
sudo systemctl start traffic-mirror
```

**Q: 如何监控应用状态？**

A: 使用多种监控方式：
```bash
# 健康检查脚本
./check_status.sh

# 系统服务状态
sudo systemctl status traffic-mirror

# 实时日志
sudo journalctl -u traffic-mirror -f

# 性能监控
top -p $(pgrep traffic-mirror)
```

---

**最后更新**: 2025-07-24
**文档版本**: v1.3.0
**适用版本**: traffic-mirror v1.0.0+

**📞 技术支持**: 如有其他问题，请查看 [GitHub Issues](https://github.com/your-org/traffic-mirror/issues) 或联系技术团队。
