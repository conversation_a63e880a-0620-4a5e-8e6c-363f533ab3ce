# 流量镜像工具文档索引

## 📚 文档导航

根据您的角色和需求，选择合适的文档：

### 🎯 按角色分类

#### 👨‍💼 项目经理 / 技术负责人
- **[OVERVIEW.md](OVERVIEW.md)** - 项目概览和价值介绍
- **[PROJECT_SUMMARY.md](PROJECT_SUMMARY.md)** - 项目技术总结
- **[README.md](README.md)** - 项目介绍和快速开始

#### 👨‍💻 开发人员
- **[README.md](README.md)** - 项目介绍和开发环境搭建
- **[RELEASE_GUIDE.md](RELEASE_GUIDE.md)** - 构建和发布指南
- **[Makefile](Makefile)** - 构建命令参考

#### 👨‍🔧 运维人员
- **[DISTRIBUTION_GUIDE.md](DISTRIBUTION_GUIDE.md)** - 发行部署指南
- **[USAGE.md](USAGE.md)** - 详细使用和运维指南
- **[quick-deploy.sh](quick-deploy.sh)** - 一键部署脚本

#### 🧪 测试人员
- **[README.md](README.md)** - 测试环境搭建
- **[test/](test/)** - 测试工具和示例
- **[docker-compose.yml](docker-compose.yml)** - 测试环境配置

### 📖 按内容分类

#### 🚀 快速开始
| 文档 | 内容 | 时间 |
|------|------|------|
| [OVERVIEW.md](OVERVIEW.md) | 项目概览，30秒了解项目 | 2分钟 |
| [README.md](README.md) | 快速开始，三种部署方式 | 5分钟 |
| [DISTRIBUTION_GUIDE.md](DISTRIBUTION_GUIDE.md) | 一键部署指南 | 3分钟 |

#### ⚙️ 配置和部署
| 文档 | 内容 | 详细程度 |
|------|------|----------|
| [README.md](README.md) | 基础配置说明 | ⭐⭐⭐ |
| [USAGE.md](USAGE.md) | 详细配置指南 | ⭐⭐⭐⭐⭐ |
| [config_prod.yaml](config_prod.yaml) | 生产环境配置模板 | ⭐⭐⭐ |
| [config_dev.yaml](config_dev.yaml) | 开发环境配置模板 | ⭐⭐⭐ |

#### 🔧 运维和监控
| 文档 | 内容 | 适用场景 |
|------|------|----------|
| [USAGE.md](USAGE.md) | 完整运维手册 | 日常运维 |
| [check_status.sh](check_status.sh) | 健康检查脚本 | 状态监控 |
| [DISTRIBUTION_GUIDE.md](DISTRIBUTION_GUIDE.md) | 部署和升级指南 | 版本管理 |

#### 🛠️ 开发和构建
| 文档 | 内容 | 目标用户 |
|------|------|----------|
| [RELEASE_GUIDE.md](RELEASE_GUIDE.md) | 完整构建发布流程 | 开发团队 |
| [Makefile](Makefile) | 构建命令集合 | 开发人员 |
| [build.sh](build.sh) | 多平台构建脚本 | CI/CD |
| [release.sh](release.sh) | 版本发布脚本 | 发布管理 |

#### 🐳 容器化部署
| 文档 | 内容 | 使用场景 |
|------|------|----------|
| [Dockerfile](Dockerfile) | 容器镜像构建 | 生产部署 |
| [docker-compose.yml](docker-compose.yml) | 开发环境编排 | 本地开发 |
| [test/Dockerfile.mock](test/Dockerfile.mock) | 模拟服务镜像 | 测试环境 |

### 🎯 按使用场景分类

#### 🆕 首次使用
1. **了解项目** → [OVERVIEW.md](OVERVIEW.md)
2. **快速体验** → [README.md](README.md) 快速开始部分
3. **正式部署** → [DISTRIBUTION_GUIDE.md](DISTRIBUTION_GUIDE.md)
4. **配置调优** → [USAGE.md](USAGE.md) 配置详解部分

#### 🔄 日常运维
1. **状态检查** → [check_status.sh](check_status.sh)
2. **问题排查** → [USAGE.md](USAGE.md) 故障排查部分
3. **性能调优** → [USAGE.md](USAGE.md) 性能调优部分
4. **版本升级** → [DISTRIBUTION_GUIDE.md](DISTRIBUTION_GUIDE.md) 升级指南

#### 🚀 版本发布
1. **构建测试** → [RELEASE_GUIDE.md](RELEASE_GUIDE.md)
2. **创建发行包** → [build.sh](build.sh)
3. **版本发布** → [release.sh](release.sh)
4. **部署验证** → [DISTRIBUTION_GUIDE.md](DISTRIBUTION_GUIDE.md)

#### 🐛 问题解决
1. **常见问题** → [USAGE.md](USAGE.md) FAQ部分
2. **故障排查** → [USAGE.md](USAGE.md) 故障排查部分
3. **日志分析** → [USAGE.md](USAGE.md) 监控和日志部分
4. **性能问题** → [USAGE.md](USAGE.md) 性能调优部分

## 🔍 快速查找

### 常用命令速查

```bash
# 构建相关
make help                    # 查看所有构建命令
make build                   # 构建本地版本
make package                 # 创建发行包
make release-patch           # 发布补丁版本

# 部署相关
sudo ./quick-deploy.sh       # 一键部署
./check_status.sh           # 健康检查
sudo systemctl status traffic-mirror  # 服务状态

# 监控相关
sudo journalctl -u traffic-mirror -f  # 实时日志
tail -f logs/request.log     # 请求日志
tail -f logs/error.log       # 错误日志
```

### 配置文件速查

```yaml
# 核心配置项
kafka:
  brokers: ["kafka:9092"]
  topic: "your-topic"
  group_id: "consumer-group"

target_api:
  url: "http://api/endpoint"
  timeout: 30
  retry_count: 3

filter:
  message_type: "SearchAPI-Request"

log:
  level: "info"
  format: "json"
```

### 故障排查速查

```bash
# 服务问题
sudo systemctl status traffic-mirror
sudo journalctl -u traffic-mirror -n 50

# 连接问题
telnet kafka-host 9092
curl -X POST http://api-host/endpoint

# 配置问题
./traffic-mirror -config config.yaml -validate

# 性能问题
top -p $(pgrep traffic-mirror)
grep "processing_rate" logs/request.log | tail -5
```

## 📋 文档维护

### 文档版本

| 文档 | 版本 | 最后更新 | 维护者 |
|------|------|----------|--------|
| README.md | v1.2.0 | 2025-07-24 | @maintainer |
| USAGE.md | v1.3.0 | 2025-07-24 | @maintainer |
| DISTRIBUTION_GUIDE.md | v1.0.0 | 2025-07-24 | @ops-team |
| RELEASE_GUIDE.md | v1.0.0 | 2025-07-24 | @dev-team |

### 更新日志

- **2025-07-24**: 完善所有文档，添加详细的运维指南
- **2025-07-24**: 添加一键部署脚本和健康检查
- **2025-07-24**: 创建完整的构建和发布系统

### 反馈和改进

如果您发现文档中的问题或有改进建议，请：

1. **提交Issue**: [GitHub Issues](https://github.com/your-org/traffic-mirror/issues)
2. **提交PR**: 直接修改文档并提交Pull Request
3. **邮件反馈**: <EMAIL>

## 🎯 学习路径

### 新手入门 (1-2小时)
1. 阅读 [OVERVIEW.md](OVERVIEW.md) 了解项目价值
2. 跟随 [README.md](README.md) 完成快速部署
3. 查看 [USAGE.md](USAGE.md) 基础配置部分

### 运维进阶 (半天)
1. 深入学习 [USAGE.md](USAGE.md) 完整内容
2. 实践 [DISTRIBUTION_GUIDE.md](DISTRIBUTION_GUIDE.md) 部署流程
3. 掌握监控和故障排查技能

### 开发专家 (1天)
1. 研究 [RELEASE_GUIDE.md](RELEASE_GUIDE.md) 构建系统
2. 了解项目架构和代码结构
3. 参与社区贡献和代码改进

---

**文档索引版本**: v1.0.0  
**最后更新**: 2025-07-24  
**维护团队**: Traffic Mirror Documentation Team

> 💡 **提示**: 建议将此文档加入书签，作为查找其他文档的入口点！
