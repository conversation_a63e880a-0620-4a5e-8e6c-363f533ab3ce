# 生产环境配置文件
kafka:
  # Kafka集群地址
  brokers:
    - "10.101.1.105:19092"
  # 消费的topic名称
  topic: "lynxiao_flow"
  # 消费者组ID
  group_id: "traffic-mirror-consumer"
  # 消费者配置
  consumer:
    # offset策略说明：
    # "newest" - 推荐：首次从最新开始，重启后从上次位置继续（实时且无遗漏）
    # "earliest" - 从最早消息开始（会处理所有历史消息，适合数据完整性要求高的场景）
    # "latest" - 总是从最新开始（可能遗漏重启期间的消息）
    offset_initial: "newest"
    # 会话超时时间（秒）
    session_timeout: 30
    # 心跳间隔（秒）
    heartbeat_interval: 3
    max_bytes: 52428800      # 50MB
    min_bytes: 512000        # 500KB
    max_wait_ms: 500         # 500ms
    read_retries: 10         # 增加重试次数
    parallel_worker: 30

# 目标API配置
target_api:
  # 目标API地址
  url: "http://10.103.240.54:50409/v1/search"
  # 请求超时时间（秒）
  timeout: 30
  # 重试次数
  retry_count: 3
  # 重试间隔（秒）
  retry_interval: 1

# 过滤配置
filter:
  # 需要处理的消息类型
  message_type: "SearchAPI-Request"

# 生产级日志配置
log:
  # 全局日志级别
  level: "error"
  # 日志格式: json, text
  format: "json"

  # 请求日志配置 - 输出到文件
  request:
    enabled: true
    output: "file"  # 只输出到文件
    file_path: "./logs/request.log"
    level: "info"

  # 统计日志配置 - 输出到控制台
  stats:
    enabled: true
    output: "stdout"  # 只输出到控制台
    level: "info"

  # 错误日志配置 - 同时输出到控制台和文件
  error:
    enabled: true
    output: "both"  # 同时输出到控制台和文件
    file_path: "./logs/error.log"
    level: "error"

  # 系统日志配置 - 输出到控制台
  system:
    enabled: true
    output: "stdout"  # 只输出到控制台
    level: "info"

# 应用配置
app:
  # 应用名称
  name: "traffic-mirror"
  # 版本
  version: "1.0.0"
  # 是否启用性能监控
  enable_metrics: true
  # 监控统计间隔（秒）
  metrics_interval: 60
