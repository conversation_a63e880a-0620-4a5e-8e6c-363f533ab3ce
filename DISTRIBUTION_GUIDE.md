# 流量镜像工具发行指南

本文档为运维人员提供完整的部署和发行指南。

## 📦 发行包概述

### 发行包结构

每个发行包包含以下文件：

```
traffic-mirror-1.0.0-linux-amd64/
├── traffic-mirror              # 主程序（已编译的二进制文件）
├── config.yaml                 # 生产环境配置文件
├── config_dev.yaml            # 开发环境配置文件
├── quick-deploy.sh            # 一键部署脚本（推荐）
├── deploy.sh                  # 原始部署脚本
├── check_status.sh            # 状态检查脚本
├── traffic-mirror.service     # systemd服务文件
├── README.md                  # 项目说明
├── USAGE.md                   # 详细使用指南
├── INSTALL.md                 # 安装说明
├── VERSION                    # 版本信息
└── logs/                      # 日志目录
```

### 支持的平台

- **Linux x64**: `traffic-mirror-1.0.0-linux-amd64.tar.gz`
- **Linux ARM64**: `traffic-mirror-1.0.0-linux-arm64.tar.gz`
- **macOS x64**: `traffic-mirror-1.0.0-darwin-amd64.tar.gz`
- **macOS ARM64**: `traffic-mirror-1.0.0-darwin-arm64.tar.gz`
- **Windows x64**: `traffic-mirror-1.0.0-windows-amd64.tar.gz`

## 🚀 运维部署指南

### 方式一：一键部署（推荐）

这是最简单的部署方式，适合大多数运维场景：

```bash
# 1. 下载并解压发行包
tar -xzf traffic-mirror-1.0.0-linux-amd64.tar.gz
cd traffic-mirror-1.0.0-linux-amd64

# 2. 预览部署过程（可选）
sudo ./quick-deploy.sh --dry-run

# 3. 一键部署
sudo ./quick-deploy.sh

# 4. 验证部署
./check_status.sh
```

### 方式二：自定义部署

如果需要自定义配置：

```bash
# 指定Kafka和API地址
sudo ./quick-deploy.sh \
  --kafka-broker "10.1.1.100:9092" \
  --kafka-topic "my_topic" \
  --target-api "http://api.example.com/v1/search"

# 自定义安装目录和用户
sudo ./quick-deploy.sh \
  --dir "/opt/my-traffic-mirror" \
  --user "my-user"
```

### 方式三：手动部署

参考 `INSTALL.md` 文件进行手动部署。

## ⚙️ 配置管理

### 核心配置项

编辑 `config.yaml` 文件，修改以下关键配置：

```yaml
# Kafka配置
kafka:
  brokers:
    - "10.101.1.105:19092"    # 修改为您的Kafka地址
  topic: "lynxiao_flow"       # 修改为您的topic名称
  group_id: "traffic-mirror-consumer"

# 目标API配置
target_api:
  url: "http://10.103.240.54:50409/v1/search"  # 修改为您的API地址
  timeout: 30
  retry_count: 3

# 日志配置
log:
  level: "info"               # 生产环境建议使用info或error
  format: "json"
```

### 环境配置建议

| 环境 | 配置文件 | 日志级别 | 特点 |
|------|----------|----------|------|
| 开发 | config_dev.yaml | debug | 控制台输出，详细日志 |
| 测试 | config.yaml | info | 文件输出，适中日志 |
| 生产 | config.yaml | error | 分层日志，性能优化 |

## 🔧 服务管理

### 基本命令

```bash
# 启动服务
sudo systemctl start traffic-mirror

# 停止服务
sudo systemctl stop traffic-mirror

# 重启服务
sudo systemctl restart traffic-mirror

# 查看状态
sudo systemctl status traffic-mirror

# 开机自启
sudo systemctl enable traffic-mirror

# 禁用自启
sudo systemctl disable traffic-mirror
```

### 日志查看

```bash
# 查看实时日志
sudo journalctl -u traffic-mirror -f

# 查看最近100行日志
sudo journalctl -u traffic-mirror -n 100

# 查看今天的日志
sudo journalctl -u traffic-mirror --since today

# 查看应用日志文件
tail -f /opt/traffic-mirror/logs/request.log
tail -f /opt/traffic-mirror/logs/error.log
```

## 📊 监控和维护

### 健康检查

```bash
# 运行健康检查脚本
cd /opt/traffic-mirror
./check_status.sh
```

健康检查包含：
- ✅ 进程运行状态
- ✅ Kafka连接状态
- ✅ 目标API连接状态
- ✅ 日志文件状态
- ✅ 系统资源使用情况

### 性能监控

```bash
# 查看处理统计
sudo journalctl -u traffic-mirror | grep "Traffic mirror statistics"

# 查看系统资源使用
top -p $(pgrep traffic-mirror)

# 查看网络连接
netstat -an | grep :19092  # Kafka连接
netstat -an | grep :50409  # API连接
```

## 🔄 升级和维护

### 升级流程

```bash
# 1. 停止服务
sudo systemctl stop traffic-mirror

# 2. 备份当前版本
sudo cp /opt/traffic-mirror/traffic-mirror \
       /opt/traffic-mirror/backup/traffic-mirror.$(date +%Y%m%d)

# 3. 解压新版本
tar -xzf traffic-mirror-1.1.0-linux-amd64.tar.gz
cd traffic-mirror-1.1.0-linux-amd64

# 4. 升级部署
sudo ./quick-deploy.sh --upgrade

# 5. 验证升级
./check_status.sh
```

### 配置备份

```bash
# 备份配置文件
sudo cp /opt/traffic-mirror/config.yaml \
       /opt/traffic-mirror/backup/config.yaml.$(date +%Y%m%d)

# 恢复配置文件
sudo cp /opt/traffic-mirror/backup/config.yaml.20250724 \
       /opt/traffic-mirror/config.yaml
sudo systemctl restart traffic-mirror
```

### 卸载应用

```bash
# 使用脚本卸载
sudo ./quick-deploy.sh --uninstall

# 手动卸载
sudo systemctl stop traffic-mirror
sudo systemctl disable traffic-mirror
sudo rm -f /etc/systemd/system/traffic-mirror.service
sudo systemctl daemon-reload
sudo rm -rf /opt/traffic-mirror
```

## 🚨 故障排查

### 常见问题

1. **服务启动失败**
   ```bash
   # 查看详细错误
   sudo journalctl -u traffic-mirror -n 50
   
   # 检查配置文件
   /opt/traffic-mirror/traffic-mirror -config /opt/traffic-mirror/config.yaml -validate
   ```

2. **Kafka连接失败**
   ```bash
   # 测试网络连通性
   telnet 10.101.1.105 19092
   
   # 检查防火墙
   sudo ufw status
   ```

3. **API转发失败**
   ```bash
   # 测试API可用性
   curl -X POST http://10.103.240.54:50409/v1/search \
     -H "Content-Type: application/json" \
     -d '{"test": "data"}'
   ```

### 日志分析

```bash
# 查看错误统计
grep -c "error" /opt/traffic-mirror/logs/error.log

# 查看处理速率
grep "processing_rate" /opt/traffic-mirror/logs/request.log | tail -5

# 查看最近的错误
grep "error" /opt/traffic-mirror/logs/error.log | tail -10
```

## 📞 技术支持

### 支持渠道

- 📖 **文档**: 查看 `USAGE.md` 详细使用指南
- 🐛 **问题反馈**: 提交GitHub Issue
- 💬 **技术交流**: 联系维护团队
- 📧 **邮件支持**: 发送技术支持邮件

### 问题报告

提交问题时请包含：

1. **环境信息**
   ```bash
   # 系统信息
   uname -a
   cat /etc/os-release
   
   # 应用版本
   cat /opt/traffic-mirror/VERSION
   
   # 服务状态
   sudo systemctl status traffic-mirror
   ```

2. **日志信息**
   ```bash
   # 最近的错误日志
   sudo journalctl -u traffic-mirror -n 100
   
   # 应用日志
   tail -100 /opt/traffic-mirror/logs/error.log
   ```

3. **配置信息**（请移除敏感信息）
   ```bash
   # 配置文件（移除密码等敏感信息）
   cat /opt/traffic-mirror/config.yaml
   ```

## 📋 部署检查清单

部署完成后请确认：

- [ ] 服务正常启动：`sudo systemctl status traffic-mirror`
- [ ] 进程正常运行：`pgrep traffic-mirror`
- [ ] Kafka连接正常：检查日志无连接错误
- [ ] API转发正常：检查日志有成功转发记录
- [ ] 日志文件正常：`ls -la /opt/traffic-mirror/logs/`
- [ ] 健康检查通过：`./check_status.sh`
- [ ] 开机自启设置：`sudo systemctl is-enabled traffic-mirror`

---

**文档版本**: v1.0.0  
**最后更新**: 2025-07-24  
**适用版本**: traffic-mirror v1.0.0+
