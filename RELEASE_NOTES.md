# Traffic Mirror v1.0.0 发布说明

## 🎉 首次正式发布

我们很高兴地宣布流量镜像工具 v1.0.0 正式发布！这是一个生产就绪的高性能Golang流量镜像工具，专为企业级部署设计。

## 📦 下载

选择适合您系统的安装包：

| 平台 | 架构 | 下载链接 | 大小 |
|------|------|----------|------|
| Linux | x64 | [traffic-mirror-1.0.0-linux-amd64.tar.gz](build/packages/traffic-mirror-1.0.0-linux-amd64.tar.gz) | ~6.4MB |
| Linux | ARM64 | [traffic-mirror-1.0.0-linux-arm64.tar.gz](build/packages/traffic-mirror-1.0.0-linux-arm64.tar.gz) | ~6.0MB |
| macOS | x64 | [traffic-mirror-1.0.0-darwin-amd64.tar.gz](build/packages/traffic-mirror-1.0.0-darwin-amd64.tar.gz) | ~6.3MB |
| macOS | ARM64 | [traffic-mirror-1.0.0-darwin-arm64.tar.gz](build/packages/traffic-mirror-1.0.0-darwin-arm64.tar.gz) | ~6.0MB |
| Windows | x64 | [traffic-mirror-1.0.0-windows-amd64.tar.gz](build/packages/traffic-mirror-1.0.0-windows-amd64.tar.gz) | ~6.5MB |

## 🚀 快速安装

### Linux系统（推荐）

```bash
# 下载并解压
tar -xzf traffic-mirror-1.0.0-linux-amd64.tar.gz
cd traffic-mirror-1.0.0-linux-amd64

# 一键部署
sudo ./quick-deploy.sh

# 验证安装
./check_status.sh
```

### 自定义部署

```bash
# 指定Kafka和API地址
sudo ./quick-deploy.sh \
  --kafka-broker "10.1.1.100:9092" \
  --kafka-topic "my_topic" \
  --target-api "http://api.example.com/v1/search"
```

### Docker部署

```bash
# 构建镜像
docker build -t traffic-mirror:1.0.0 .

# 运行容器
docker run -d --name traffic-mirror \
  -v $(pwd)/config.yaml:/app/config.yaml:ro \
  -v $(pwd)/logs:/app/logs \
  traffic-mirror:1.0.0
```

## ✨ 主要特性

### 🔥 核心功能
- **实时消息处理**: 从Kafka实时消费消息，只处理最新消息
- **智能消息过滤**: 支持按消息类型过滤，避免无效处理
- **自动消息解析**: 智能解析SearchAPI请求数据
- **HTTP请求转发**: 高性能HTTP转发到目标API
- **完善错误处理**: 自动重试机制和故障恢复
- **优雅关闭**: 支持信号处理，确保数据完整性

### 🔥 生产级特性
- **分层日志系统**: 请求日志文件化，统计日志控制台化
- **日志自动轮转**: 按大小和时间自动切割，保留7天历史
- **实时监控统计**: 处理速率、成功率、延迟等关键指标
- **多环境配置**: 开发和生产环境独立配置
- **高可用设计**: 支持多实例部署和负载均衡

### 🔥 运维友好
- **一键部署**: 运维人员只需三步完成部署
- **健康检查**: 自动检查系统状态和连接
- **自动化构建**: 支持多平台自动构建和发布
- **完整文档**: 详细的使用和部署指南

## 📊 性能指标

### 正常性能
- 处理速率: 300-500 msg/s
- 转发成功率: >99%
- 平均延迟: <200ms
- 内存使用: <100MB
- CPU使用: <50%

### 高性能配置
- 处理速率: 500-1000 msg/s
- 转发成功率: >99.5%
- 平均延迟: <100ms
- 内存使用: <200MB
- CPU使用: <80%

## 🔧 系统要求

### 最小配置
- CPU: 2核心
- 内存: 1GB
- 磁盘: 10GB
- 网络: 稳定的内网连接

### 推荐配置
- CPU: 4核心
- 内存: 2GB
- 磁盘: 50GB
- 网络: 高速内网连接

### 软件要求
- Linux系统（推荐CentOS 7+/Ubuntu 18.04+）
- systemd支持
- 可访问的Kafka集群
- 目标API服务

## 📋 配置示例

### 生产环境配置

```yaml
kafka:
  brokers:
    - "10.101.1.105:19092"
  topic: "lynxiao_flow"
  group_id: "traffic-mirror-consumer-prod"

target_api:
  url: "http://10.103.240.54:50409/v1/search"
  timeout: 30
  retry_count: 3

log:
  level: "error"
  format: "json"
  request:
    enabled: true
    output: "file"
    file_path: "./logs/request.log"
  stats:
    enabled: true
    output: "stdout"
  error:
    enabled: true
    output: "both"
    file_path: "./logs/error.log"
```

## 🔧 管理命令

### 服务管理
```bash
sudo systemctl start traffic-mirror    # 启动服务
sudo systemctl stop traffic-mirror     # 停止服务
sudo systemctl restart traffic-mirror  # 重启服务
sudo systemctl status traffic-mirror   # 查看状态
```

### 日志查看
```bash
sudo journalctl -u traffic-mirror -f           # 实时日志
tail -f /opt/traffic-mirror/logs/request.log   # 请求日志
tail -f /opt/traffic-mirror/logs/error.log     # 错误日志
```

### 健康检查
```bash
cd /opt/traffic-mirror
./check_status.sh  # 运行健康检查
```

## 🔐 安全特性

- **专用用户**: 使用专用的traffic-mirror用户运行
- **最小权限**: 遵循最小权限原则
- **文件权限**: 严格的文件访问控制
- **网络安全**: 支持防火墙和网络策略
- **日志安全**: 避免记录敏感信息

## 📖 文档资源

- **[USAGE.md](USAGE.md)**: 详细使用指南
- **[DISTRIBUTION_GUIDE.md](DISTRIBUTION_GUIDE.md)**: 运维部署指南
- **[RELEASE_GUIDE.md](RELEASE_GUIDE.md)**: 发布和构建指南
- **[PROJECT_SUMMARY.md](PROJECT_SUMMARY.md)**: 项目总结

## 🔍 校验和

请使用以下命令验证下载文件的完整性：

```bash
sha256sum -c traffic-mirror-1.0.0-checksums.txt
```

校验和文件：
```
abf50ee39001c99f00358dbdb0bf881b68513b802e7128d5ba30a56686952758  traffic-mirror-1.0.0-darwin-amd64.tar.gz
0209e6ea5c66f95e6472785779f80383706cc5ca6b0e639ac8bd8f9c34f9e1f6  traffic-mirror-1.0.0-darwin-arm64.tar.gz
7957e723cfa37ebaf6d779ebbe3d6145773899db9a74b1cb18045e90e95964b9  traffic-mirror-1.0.0-linux-amd64.tar.gz
de304796787acb1fb88c19c0d2da2e9433fc9c6d8572aada72bfd7beea6fec58  traffic-mirror-1.0.0-linux-arm64.tar.gz
aac6bcc17f77e64e4ee7a4a5f4724b0522486397785005326384ce2c2aa8fdbd  traffic-mirror-1.0.0-windows-amd64.tar.gz
```

## 🆙 升级指南

从旧版本升级：

```bash
# 停止服务
sudo systemctl stop traffic-mirror

# 备份配置
sudo cp /opt/traffic-mirror/config.yaml /opt/traffic-mirror/config.yaml.backup

# 解压新版本
tar -xzf traffic-mirror-1.0.0-linux-amd64.tar.gz
cd traffic-mirror-1.0.0-linux-amd64

# 升级部署
sudo ./quick-deploy.sh --upgrade

# 验证升级
./check_status.sh
```

## 🐛 已知问题

目前没有已知的重大问题。如果遇到问题，请：

1. 查看 [USAGE.md](USAGE.md) 故障排查部分
2. 检查日志文件
3. 提交 GitHub Issue

## 📞 技术支持

- 📖 **文档**: 查看完整文档
- 🐛 **问题反馈**: [GitHub Issues](https://github.com/your-org/traffic-mirror/issues)
- 💬 **技术交流**: 联系维护团队
- 📧 **邮件支持**: <EMAIL>

## 🙏 致谢

感谢所有参与项目开发和测试的团队成员！

---

**发布时间**: 2025-07-24  
**发布版本**: v1.0.0  
**Git提交**: aeb0af7  
**构建时间**: 2025-07-24_09:21:16
