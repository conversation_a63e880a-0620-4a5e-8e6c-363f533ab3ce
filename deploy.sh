#!/bin/bash

# 流量镜像工具生产环境部署脚本
# 用途：自动化部署流量镜像工具到生产环境

set -e  # 遇到错误立即退出

# 配置变量
APP_NAME="traffic-mirror"
APP_USER="traffic-mirror"
INSTALL_DIR="/opt/traffic-mirror"
SERVICE_FILE="/etc/systemd/system/traffic-mirror.service"
CONFIG_FILE="config_prod.yaml"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."

    # 检查操作系统
    if ! command -v systemctl &> /dev/null; then
        log_error "需要systemd支持的Linux系统"
        exit 1
    fi

    # 检查磁盘空间（至少需要1GB）
    available_space=$(df / | awk 'NR==2 {print $4}')
    if [ $available_space -lt 1048576 ]; then  # 1GB in KB
        log_warn "可用磁盘空间不足1GB，建议清理磁盘空间"
    fi

    log_info "系统要求检查通过"
}

# 创建应用用户
create_user() {
    if id "$APP_USER" &>/dev/null; then
        log_info "用户 $APP_USER 已存在"
    else
        log_info "创建应用用户 $APP_USER"
        useradd -r -s /bin/false -d $INSTALL_DIR $APP_USER
    fi
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."

    mkdir -p $INSTALL_DIR
    mkdir -p $INSTALL_DIR/logs
    mkdir -p $INSTALL_DIR/backup

    # 设置权限
    chown -R $APP_USER:$APP_USER $INSTALL_DIR
    chmod 755 $INSTALL_DIR
    chmod 755 $INSTALL_DIR/logs
    chmod 755 $INSTALL_DIR/backup
}

# 编译应用
build_application() {
    log_info "编译应用..."

    if ! command -v go &> /dev/null; then
        log_error "未找到Go编译器，请先安装Go 1.19+"
        exit 1
    fi

    # 检查源码文件
    if [ ! -f "main.go" ]; then
        log_error "未找到main.go文件，请在项目根目录运行此脚本"
        exit 1
    fi

    # 安装依赖
    go mod tidy

    # 编译
    CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o $APP_NAME .

    if [ ! -f "$APP_NAME" ]; then
        log_error "编译失败"
        exit 1
    fi

    log_info "编译完成"
}

# 安装应用文件
install_files() {
    log_info "安装应用文件..."

    # 备份现有文件（如果存在）
    if [ -f "$INSTALL_DIR/$APP_NAME" ]; then
        log_info "备份现有应用文件"
        cp $INSTALL_DIR/$APP_NAME $INSTALL_DIR/backup/$APP_NAME.$(date +%Y%m%d_%H%M%S)
    fi

    # 复制新文件
    cp $APP_NAME $INSTALL_DIR/
    cp $CONFIG_FILE $INSTALL_DIR/

    # 设置权限
    chown $APP_USER:$APP_USER $INSTALL_DIR/$APP_NAME
    chown $APP_USER:$APP_USER $INSTALL_DIR/$CONFIG_FILE
    chmod 755 $INSTALL_DIR/$APP_NAME
    chmod 644 $INSTALL_DIR/$CONFIG_FILE

    log_info "应用文件安装完成"
}

# 创建systemd服务
create_service() {
    log_info "创建systemd服务..."

    cat > $SERVICE_FILE << EOF
[Unit]
Description=Traffic Mirror Service
Documentation=https://github.com/your-org/traffic-mirror
After=network.target

[Service]
Type=simple
User=$APP_USER
Group=$APP_USER
WorkingDirectory=$INSTALL_DIR
ExecStart=$INSTALL_DIR/$APP_NAME -config $INSTALL_DIR/$CONFIG_FILE
ExecReload=/bin/kill -HUP \$MAINPID
KillMode=process
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$APP_NAME

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$INSTALL_DIR/logs

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd配置
    systemctl daemon-reload

    log_info "systemd服务创建完成"
}

# 配置系统参数
configure_system() {
    log_info "配置系统参数..."

    # 设置文件描述符限制
    if ! grep -q "$APP_USER.*nofile" /etc/security/limits.conf; then
        echo "$APP_USER soft nofile 65536" >> /etc/security/limits.conf
        echo "$APP_USER hard nofile 65536" >> /etc/security/limits.conf
    fi

    # 优化网络参数
    if ! grep -q "net.core.rmem_max" /etc/sysctl.conf; then
        echo "net.core.rmem_max = 16777216" >> /etc/sysctl.conf
        echo "net.core.wmem_max = 16777216" >> /etc/sysctl.conf
        sysctl -p
    fi

    log_info "系统参数配置完成"
}

# 启动服务
start_service() {
    log_info "启动服务..."

    # 启用服务
    systemctl enable $APP_NAME

    # 启动服务
    systemctl start $APP_NAME

    # 等待服务启动
    sleep 3

    # 检查服务状态
    if systemctl is-active --quiet $APP_NAME; then
        log_info "服务启动成功"
    else
        log_error "服务启动失败"
        systemctl status $APP_NAME
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."

    # 检查进程
    if pgrep -f "$APP_NAME" > /dev/null; then
        log_info "✓ 进程运行正常"
    else
        log_error "✗ 进程未运行"
        return 1
    fi

    # 检查日志文件
    sleep 5
    if [ -f "$INSTALL_DIR/logs/request.log" ]; then
        log_info "✓ 日志文件创建成功"
    else
        log_warn "⚠ 日志文件尚未创建（可能需要等待消息处理）"
    fi

    # 显示服务状态
    systemctl status $APP_NAME --no-pager

    log_info "部署验证完成"
}

# 显示部署后信息
show_post_deployment_info() {
    log_info "部署完成！"
    echo
    echo "服务管理命令："
    echo "  启动服务: sudo systemctl start $APP_NAME"
    echo "  停止服务: sudo systemctl stop $APP_NAME"
    echo "  重启服务: sudo systemctl restart $APP_NAME"
    echo "  查看状态: sudo systemctl status $APP_NAME"
    echo
    echo "日志查看命令："
    echo "  实时日志: sudo journalctl -u $APP_NAME -f"
    echo "  请求日志: tail -f $INSTALL_DIR/logs/request.log"
    echo "  错误日志: tail -f $INSTALL_DIR/logs/error.log"
    echo
    echo "配置文件位置: $INSTALL_DIR/$CONFIG_FILE"
    echo "应用目录: $INSTALL_DIR"
}

# 主函数
main() {
    log_info "开始部署流量镜像工具..."

    check_root
    check_requirements
    create_user
    create_directories
    build_application
    install_files
    create_service
    configure_system
    start_service
    verify_deployment
    show_post_deployment_info

    log_info "部署完成！"
}

# 执行主函数
main "$@"
