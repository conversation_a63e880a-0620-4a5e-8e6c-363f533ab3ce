[Unit]
Description=Traffic Mirror Tool - Kafka to HTTP Forwarder
Documentation=https://github.com/your-org/traffic-mirror
After=network.target
Wants=network.target

[Service]
Type=simple
User=traffic-mirror
Group=traffic-mirror
WorkingDirectory=/opt/traffic-mirror
ExecStart=/opt/traffic-mirror/traffic-mirror -config /opt/traffic-mirror/config.yaml
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=traffic-mirror

# 资源限制
LimitNOFILE=65536
LimitNPROC=32768

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/traffic-mirror/logs

# 环境变量
Environment=GOMAXPROCS=2

[Install]
WantedBy=multi-user.target
